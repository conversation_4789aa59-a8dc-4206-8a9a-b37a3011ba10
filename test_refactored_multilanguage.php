<?php

require_once __DIR__ . '/vendor/autoload.php';

use app\ai\services\MultiLanguageSegmentationService;
use app\ai\services\ChineseSegmentationService;
use app\ai\services\EnglishSegmentationService;
use app\ai\services\JapaneseSegmentationService;
use app\ai\services\KoreanSegmentationService;

echo "🏗️ 重构后的多语言分词架构测试\n";
echo "=" . str_repeat("=", 70) . "\n\n";

try {
    // 创建统一分发器
    $multiLangService = new MultiLanguageSegmentationService();
    
    echo "📋 架构设计说明：\n";
    echo "✅ ChineseSegmentationService - 专门处理中文\n";
    echo "✅ EnglishSegmentationService - 专门处理英文\n";
    echo "✅ JapaneseSegmentationService - 专门处理日文\n";
    echo "✅ KoreanSegmentationService - 专门处理韩文\n";
    echo "✅ MultiLanguageSegmentationService - 统一分发器\n";
    echo "✅ 其他语言使用通用分词算法\n\n";
    
    // 显示可用服务
    echo "🔧 可用的分词服务：\n";
    $availableServices = $multiLangService->getAvailableServices();
    foreach ($availableServices as $lang => $info) {
        $status = $info['is_specialized'] ? '专门服务' : '通用分词';
        echo "   {$lang} ({$info['language_name']}): {$status}\n";
    }
    echo "\n";
    
    // 核心语言测试
    $coreLanguageTests = [
        [
            'text' => '怎么在淘宝购买商品',
            'expected_lang' => 'zh',
            'expected_service' => 'ChineseSegmentationService',
            'description' => '中文测试 - 使用专门的中文分词服务'
        ],
        [
            'text' => 'How to buy products on Amazon',
            'expected_lang' => 'en',
            'expected_service' => 'EnglishSegmentationService',
            'description' => '英文测试 - 使用专门的英文分词服务'
        ],
        [
            'text' => 'アマゾンで商品を購入する方法',
            'expected_lang' => 'ja',
            'expected_service' => 'JapaneseSegmentationService',
            'description' => '日文测试 - 使用专门的日文分词服务'
        ],
        [
            'text' => '쿠팡에서 상품 구매하는 방법',
            'expected_lang' => 'ko',
            'expected_service' => 'KoreanSegmentationService',
            'description' => '韩文测试 - 使用专门的韩文分词服务'
        ],
        [
            'text' => 'كيفية شراء المنتجات على أمازون',
            'expected_lang' => 'ar',
            'expected_service' => 'universal',
            'description' => '阿拉伯文测试 - 使用通用分词'
        ]
    ];
    
    echo "🎯 核心语言分词测试\n";
    echo "-" . str_repeat("-", 60) . "\n\n";
    
    foreach ($coreLanguageTests as $index => $test) {
        echo "📝 测试 " . ($index + 1) . ": {$test['description']}\n";
        echo "   输入: \"{$test['text']}\"\n";
        
        // 语言检测
        $detectedLanguages = $multiLangService->detectLanguages($test['text']);
        $primaryLang = array_key_first($detectedLanguages);
        $confidence = $detectedLanguages[$primaryLang] ?? 0;
        
        echo "   检测语言: {$primaryLang} (置信度: " . number_format($confidence * 100, 1) . "%)\n";
        
        // 分词结果
        $keywords = $multiLangService->extractKeywords($test['text'], 8);
        echo "   关键词: [" . implode(', ', $keywords) . "]\n";
        
        // 调试信息
        $debug = $multiLangService->debugSegmentation($test['text']);
        $actualService = $debug['specialized_service'] ?? 'universal';
        $actualServiceName = $actualService ? basename(str_replace('\\', '/', $actualService)) : 'universal';
        
        echo "   使用服务: {$actualServiceName}\n";
        echo "   期望服务: {$test['expected_service']}\n";
        
        $serviceMatch = ($actualServiceName === $test['expected_service']) || 
                       ($actualService === null && $test['expected_service'] === 'universal');
        
        echo "   服务匹配: " . ($serviceMatch ? "✅ 正确" : "❌ 错误") . "\n";
        
        echo "\n" . str_repeat("-", 60) . "\n\n";
    }
    
    // 专门服务详细测试
    echo "🔍 专门服务详细测试\n";
    echo "=" . str_repeat("=", 70) . "\n\n";
    
    // 中文分词服务测试
    echo "1. 中文分词服务测试\n";
    $chineseService = new ChineseSegmentationService();
    $chineseText = "在淘宝天猫购买iPhone手机支付宝付款";
    $chineseKeywords = $chineseService->extractKeywords($chineseText);
    echo "   文本: {$chineseText}\n";
    echo "   关键词: [" . implode(', ', array_slice($chineseKeywords, 0, 6)) . "]\n";
    echo "   特点: 中文词汇分离、电商平台识别、支付方式识别\n\n";
    
    // 英文分词服务测试
    echo "2. 英文分词服务测试\n";
    $englishService = new EnglishSegmentationService();
    $englishText = "How to buy iPhone on Amazon using PayPal payment";
    $englishKeywords = $englishService->extractKeywords($englishText);
    echo "   文本: {$englishText}\n";
    echo "   关键词: [" . implode(', ', array_slice($englishKeywords, 0, 6)) . "]\n";
    echo "   特点: 词根处理、组合词识别、业务词汇优先\n\n";
    
    // 日文分词服务测试
    echo "3. 日文分词服务测试\n";
    $japaneseService = new JapaneseSegmentationService();
    $japaneseText = "楽天でスマートフォンを購入する方法";
    $japaneseKeywords = $japaneseService->extractKeywords($japaneseText);
    echo "   文本: {$japaneseText}\n";
    echo "   关键词: [" . implode(', ', array_slice($japaneseKeywords, 0, 6)) . "]\n";
    echo "   特点: 平假名片假名汉字分离、日文电商平台识别\n\n";
    
    // 韩文分词服务测试
    echo "4. 韩文分词服务测试\n";
    $koreanService = new KoreanSegmentationService();
    $koreanText = "네이버에서 노트북 주문하고 카카오페이로 결제";
    $koreanKeywords = $koreanService->extractKeywords($koreanText);
    echo "   文本: {$koreanText}\n";
    echo "   关键词: [" . implode(', ', array_slice($koreanKeywords, 0, 6)) . "]\n";
    echo "   特点: 韩文语法处理、韩文电商平台识别\n\n";
    
    // 性能对比测试
    echo "⚡ 性能对比测试\n";
    echo "=" . str_repeat("=", 70) . "\n\n";
    
    $performanceTests = [
        '中文' => '怎么在淘宝购买商品并用支付宝付款',
        '英文' => 'How to buy products on Amazon using credit card',
        '日文' => '楽天でスマートフォンを購入する方法',
        '韩文' => '쿠팡에서 상품 구매하고 카카오페이 결제',
        '混合' => '在Amazon购买iPhone using PayPal支付'
    ];
    
    foreach ($performanceTests as $type => $text) {
        $startTime = microtime(true);
        
        for ($i = 0; $i < 50; $i++) {
            $multiLangService->extractKeywords($text);
        }
        
        $endTime = microtime(true);
        $avgTime = ($endTime - $startTime) / 50;
        
        echo "{$type}: " . number_format($avgTime * 1000, 2) . "ms/次\n";
    }
    echo "\n";
    
    // 服务管理测试
    echo "🔧 服务管理测试\n";
    echo "=" . str_repeat("=", 70) . "\n\n";
    
    echo "1. 预加载服务测试\n";
    $preloadLanguages = ['zh', 'en', 'ja', 'ko'];
    $loaded = $multiLangService->preloadServices($preloadLanguages);
    echo "   预加载语言: [" . implode(', ', $preloadLanguages) . "]\n";
    echo "   成功加载: [" . implode(', ', $loaded) . "]\n";
    echo "   加载率: " . number_format(count($loaded) / count($preloadLanguages) * 100, 1) . "%\n\n";
    
    echo "2. 服务状态检查\n";
    $services = $multiLangService->getAvailableServices();
    $specializedCount = 0;
    $loadedCount = 0;
    
    foreach ($services as $lang => $info) {
        if ($info['is_specialized']) $specializedCount++;
        if ($info['is_loaded']) $loadedCount++;
    }
    
    echo "   总语言数: " . count($services) . "\n";
    echo "   专门服务数: {$specializedCount}\n";
    echo "   已加载服务数: {$loadedCount}\n\n";
    
    echo "3. 服务清理测试\n";
    $multiLangService->cleanupServices();
    echo "   清理完成 ✅\n\n";
    
    // 架构优势总结
    echo "🎉 重构架构优势总结\n";
    echo "=" . str_repeat("=", 70) . "\n";
    echo "✅ 模块化设计\n";
    echo "   - 每种语言有专门的分词服务\n";
    echo "   - 统一的接口和调用方式\n";
    echo "   - 易于扩展和维护\n\n";
    
    echo "✅ 性能优化\n";
    echo "   - 延迟加载，按需实例化\n";
    echo "   - 专门算法，针对性优化\n";
    echo "   - 服务复用，避免重复创建\n\n";
    
    echo "✅ 扩展性强\n";
    echo "   - 新增语言只需创建对应服务类\n";
    echo "   - 不影响现有语言的处理\n";
    echo "   - 支持服务热插拔\n\n";
    
    echo "✅ 向后兼容\n";
    echo "   - 保持原有API接口不变\n";
    echo "   - 自动降级到通用分词\n";
    echo "   - 渐进式升级支持\n\n";
    
    echo "💡 使用建议\n";
    echo "   1. 优先使用 MultiLanguageSegmentationService 统一入口\n";
    echo "   2. 根据业务需求预加载常用语言服务\n";
    echo "   3. 定期清理未使用的服务实例\n";
    echo "   4. 监控各语言分词效果和性能\n";
    echo "   5. 根据用户反馈持续优化词典\n\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误:\n";
    echo "错误信息: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "\n堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "🎉 重构后的多语言分词架构测试完成!\n";
echo "现在拥有了更加模块化、高性能的多语言分词能力。\n";
