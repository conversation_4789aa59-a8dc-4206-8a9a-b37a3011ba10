<?php

namespace app\vchat\auto_reply;

use app\ai\services\KnowledgeBaseService;
use app\ai\utils\Logger;

/**
 * 修复后的帮助回复类
 * 支持灵活的回复格式配置
 */
class HelpReplyFixed implements AutoReplyInterface
{
    /**
     * 知识库服务
     * @var KnowledgeBaseService
     */
    protected KnowledgeBaseService $knowledgeBase;

    /**
     * 回复配置
     * @var array
     */
    protected array $config;

    public function __construct(array $config = [])
    {
        // 默认配置 - 推荐设置
        $defaultConfig = [
            'mode' => 'simple',                    // 简洁模式
            'include_fallback_message' => false,   // 不显示客服提示
            'include_suggestions' => true,         // 显示相关建议
            'max_content_length' => 300,          // 最大300字
            'confidence_threshold' => 0.3         // 置信度阈值
        ];

        $this->config = array_merge($defaultConfig, $config);
        
        // 创建知识库服务
        $this->knowledgeBase = new KnowledgeBaseService($this->config);
        
        Logger::info('HelpReplyFixed initialized', [
            'mode' => $this->config['mode'],
            'fallback_message' => $this->config['include_fallback_message']
        ]);
    }

    /**
     * 检查是否应该回复
     * @param array $message
     * @return bool
     */
    public function shouldReply(array $message): bool
    {
        $content = $message['content'] ?? '';
        
        // 检查是否包含帮助相关关键词
        $helpKeywords = [
            '帮助', '怎么', '如何', '怎样', '教程', '说明', 
            '操作', '使用', '功能', '设置', '配置', '问题',
            '不会', '不懂', '不知道', '求助', '指导'
        ];
        
        foreach ($helpKeywords as $keyword) {
            if (strpos($content, $keyword) !== false) {
                Logger::info('Help keyword detected', [
                    'keyword' => $keyword,
                    'content' => $content
                ]);
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取回复（接口方法）
     * @param array $message
     * @return string|null
     */
    public function getReply(array $message): ?string
    {
        $result = $this->generateReply($message);
        return $result ? $result['content'] : null;
    }

    /**
     * 生成回复
     * @param array $message
     * @return array|null
     */
    public function generateReply(array $message): ?array
    {
        try {
            $question = $message['content'] ?? '';
            $sessionId = $message['session_id'] ?? 'help_' . uniqid();
            
            Logger::info('Generating help reply', [
                'question' => $question,
                'session_id' => $sessionId,
                'mode' => $this->config['mode']
            ]);
            
            // 根据问题长度和复杂度动态调整模式
            $this->adjustModeByQuestion($question);
            
            // 调用知识库服务
            $result = $this->knowledgeBase->ask($question, [
                'session_id' => $sessionId,
                'use_memory' => true
            ]);
            
            if (!$result['success']) {
                return $this->generateFallbackReply($question);
            }
            
            // 检查置信度
            if ($result['confidence'] < $this->config['confidence_threshold']) {
                Logger::info('Low confidence response', [
                    'confidence' => $result['confidence'],
                    'threshold' => $this->config['confidence_threshold']
                ]);
                return $this->generateLowConfidenceReply($question, $result);
            }
            
            // 格式化回复
            $reply = $this->formatReply($result);
            
            Logger::info('Help reply generated successfully', [
                'confidence' => $result['confidence'],
                'sources_count' => count($result['sources'] ?? []),
                'content_length' => strlen($reply['content'])
            ]);
            
            return $reply;
            
        } catch (\Exception $e) {
            Logger::error('Failed to generate help reply', [
                'question' => $question ?? '',
                'error' => $e->getMessage()
            ]);
            
            return $this->generateErrorReply();
        }
    }

    /**
     * 根据问题动态调整回复模式
     * @param string $question
     */
    protected function adjustModeByQuestion(string $question): void
    {
        $length = mb_strlen($question);
        
        // 复杂问题关键词
        $complexKeywords = ['详细', '具体', '完整', '全面', '步骤', '教程', '指南'];
        $isComplex = false;
        
        foreach ($complexKeywords as $keyword) {
            if (strpos($question, $keyword) !== false) {
                $isComplex = true;
                break;
            }
        }
        
        // 动态调整模式
        if ($isComplex || $length > 50) {
            $this->knowledgeBase->setResponseMode('detailed');
            Logger::info('Switched to detailed mode', ['reason' => 'complex_question']);
        } elseif ($length > 20) {
            $this->knowledgeBase->setResponseMode('formal');
            Logger::info('Switched to formal mode', ['reason' => 'medium_question']);
        } else {
            $this->knowledgeBase->setResponseMode('simple');
            Logger::info('Using simple mode', ['reason' => 'simple_question']);
        }
    }

    /**
     * 格式化回复内容
     * @param array $result
     * @return array
     */
    protected function formatReply(array $result): array
    {
        $content = $result['content'];
        
        // 限制内容长度
        if (strlen($content) > $this->config['max_content_length']) {
            $content = mb_substr($content, 0, $this->config['max_content_length']) . '...';
        }
        
        $reply = [
            'type' => 'help',
            'content' => $content,
            'confidence' => $result['confidence'],
            'sources' => $result['sources'] ?? []
        ];
        
        // 添加相关建议（如果启用）
        if ($this->config['include_suggestions'] && !empty($result['suggestions'])) {
            $suggestions = array_slice($result['suggestions'], 0, 3);
            $reply['suggestions'] = $suggestions;
            
            // 在内容后添加建议
            if (!empty($suggestions)) {
                $suggestionText = "\n\n相关帮助：";
                foreach ($suggestions as $suggestion) {
                    $suggestionText .= "\n• " . $suggestion['title'];
                }
                $reply['content'] .= $suggestionText;
            }
        }
        
        return $reply;
    }

    /**
     * 生成低置信度回复
     * @param string $question
     * @param array $result
     * @return array
     */
    protected function generateLowConfidenceReply(string $question, array $result): array
    {
        $content = "抱歉，我对您的问题不太确定。";
        
        // 如果有部分相关内容，提供参考
        if (!empty($result['sources'])) {
            $content .= "您可以参考以下相关信息：\n\n";
            foreach (array_slice($result['sources'], 0, 2) as $source) {
                $content .= "• " . $source['title'] . "\n";
            }
        }
        
        $content .= "\n建议您联系人工客服获得更准确的帮助。";
        
        return [
            'type' => 'help_uncertain',
            'content' => $content,
            'confidence' => $result['confidence'],
            'sources' => $result['sources'] ?? []
        ];
    }

    /**
     * 生成备用回复
     * @param string $question
     * @return array
     */
    protected function generateFallbackReply(string $question): array
    {
        return [
            'type' => 'help_fallback',
            'content' => '抱歉，我暂时无法回答您的问题。建议您查看帮助文档或联系客服。',
            'confidence' => 0.1
        ];
    }

    /**
     * 生成错误回复
     * @return array
     */
    protected function generateErrorReply(): array
    {
        return [
            'type' => 'help_error',
            'content' => '系统暂时繁忙，请稍后再试。',
            'confidence' => 0.0
        ];
    }

    /**
     * 设置回复模式
     * @param string $mode
     * @return self
     */
    public function setMode(string $mode): self
    {
        $this->config['mode'] = $mode;
        $this->knowledgeBase->setResponseMode($mode);
        return $this;
    }

    /**
     * 设置配置
     * @param array $config
     * @return self
     */
    public function setConfig(array $config): self
    {
        $this->config = array_merge($this->config, $config);
        $this->knowledgeBase->setResponseConfig($this->config);
        return $this;
    }

    /**
     * 获取当前配置
     * @return array
     */
    public function getConfig(): array
    {
        return $this->config;
    }
}
