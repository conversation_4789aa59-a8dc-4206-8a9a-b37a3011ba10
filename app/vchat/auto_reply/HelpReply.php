<?php
namespace app\vchat\auto_reply;

use app\model\Help;
use app\ai\services\KnowledgeBaseService;
use think\facade\Cache;

class HelpReply extends AbstractAutoReply
{
    private $cacheKey = 'vchat_help_reply_cache';

    /**
     * AI知识库服务
     * @var KnowledgeBaseService
     */
    private $knowledgeBaseService;

    public function __construct()
    {
        $this->knowledgeBaseService = new KnowledgeBaseService();
    }

    public function shouldReply(array $message): bool
    {
        // 验证消息格式
        if (!$this->validateMessage($message, 'content', 'string')) {
            return false;
        }

        return true;
    }

    /**
     * 获取自动回复内容
     * @param array $message
     * @return string|null
     */
    public function getReply(array $message): ?string
    {
        $content = $message['content'] ?? '';
        $fromId = $message['from_id'] ?? 0;

        // 首先尝试AI智能回答
        $aiResult = $this->getAiReply($content, $fromId);
        if ($aiResult && $aiResult['confidence'] > 0.6) {
            return $aiResult['answer'];
        }

        // 如果AI回答置信度不高，回退到传统匹配
        return $this->getTraditionalReply($content);
    }

    /**
     * 获取AI智能回复
     * @param string $content
     * @param int $fromId
     * @return array|null
     */
    protected function getAiReply(string $content, int $fromId): ?array
    {
        try {
            $result = $this->knowledgeBaseService->ask($content, [
                'session_id' => 'help_' . $fromId,
                'use_memory' => true
            ]);

            if ($result['success']) {
                return [
                    'answer' => $result['content'],
                    'confidence' => $result['confidence'],
                    'sources' => $result['sources']
                ];
            }
        } catch (\Exception $e) {
            // AI服务异常时记录日志但不影响传统回复
            error_log('AI help reply failed: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * 获取传统匹配回复
     * @param string $content
     * @return string|null
     */
    protected function getTraditionalReply(string $content): ?string
    {
        $helps = $this->getCachedHelps();

        foreach ($helps as $help) {
            if ($this->matchHelp($content, $help['title'])) {
                return $this->formatHelpContent($help);
            }
        }

        return null;
    }

    protected function getCachedHelps()
    {
        // 从缓存获取帮助内容
        $helps = Cache::get($this->cacheKey);

        if (!$helps) {
            $helps = Help::where('enabled', 1)
                ->field('title, content')
                ->order('sort', 'desc')
                ->select()
                ->toArray();

            // 缓存10分钟
            Cache::set($this->cacheKey, $helps, 600);
        }

        return $helps;
    }

    protected function matchHelp(string $content, string $title): bool
    {
        return strpos($content, $title) !== false;
    }

    /**
     * 格式化帮助内容
     * @param array $help
     * @return string
     */
    protected function formatHelpContent(array $help): string
    {
        $content = strip_tags($help['content']);

        // 限制长度
        if (mb_strlen($content) > 200) {
            $content = mb_substr($content, 0, 200) . '...';
        }

        return "📋 {$help['title']}\n\n{$content}\n\n如需更多帮助，请输入 @AI 获取智能回答。";
    }


}