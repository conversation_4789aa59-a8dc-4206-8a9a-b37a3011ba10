<?php

namespace app\ai\memory;

use app\ai\interfaces\MemoryInterface;
use app\ai\exceptions\AiServiceException;
use app\ai\utils\Logger;
use app\ai\models\AiChatSession;
use app\ai\models\AiChatMessage;
use app\ai\models\AiChatContext;
use think\Exception;

/**
 * MySQL记忆存储实现（使用ThinkPHP模型）
 * 基于现有的AI模型类实现记忆存储功能
 */
class MySqlMemoryNew implements MemoryInterface
{
    /**
     * 最大历史记录长度
     * @var int
     */
    protected int $maxHistoryLength = 100;

    /**
     * 构造函数
     * @param array $config 配置参数
     */
    public function __construct(array $config = [])
    {
        if (isset($config['max_history_length'])) {
            $this->maxHistoryLength = $config['max_history_length'];
        }
        
        Logger::info('MySqlMemory initialized using ThinkPHP models');
    }

    /**
     * 保存消息到记忆中
     * @param string $sessionId 会话ID
     * @param array $message 消息内容，包含input和output
     * @return void
     */
    public function saveMessage(string $sessionId, array $message): void
    {
        try {
            // 确保会话存在
            $this->ensureSessionExists($sessionId);
            
            // 保存输入消息
            if (isset($message['input'])) {
                AiChatMessage::addInputMessage(
                    $sessionId, 
                    $message['input'], 
                    $message['input_metadata'] ?? []
                );
            }
            
            // 保存输出消息
            if (isset($message['output'])) {
                AiChatMessage::addOutputMessage(
                    $sessionId, 
                    $message['output'], 
                    [
                        'metadata' => $message['output_metadata'] ?? [],
                        'token_count' => $message['token_count'] ?? 0
                    ]
                );
            }
            
            // 更新会话活跃时间
            $this->updateSessionActivity($sessionId);
            
            // 清理旧消息（保持在最大历史记录数内）
            $this->cleanupOldMessages($sessionId);
            
            Logger::info('Message saved to MySQL memory', [
                'session_id' => $sessionId,
                'has_input' => isset($message['input']),
                'has_output' => isset($message['output'])
            ]);
            
        } catch (Exception $e) {
            Logger::error('Failed to save message to MySQL memory', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            throw new AiServiceException("Failed to save message: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * 获取会话历史记录
     * @param string $sessionId 会话ID
     * @param int $limit 限制数量
     * @return array
     */
    public function getHistory(string $sessionId, int $limit = 50): array
    {
        try {
            $messages = AiChatMessage::getSessionHistory($sessionId, $limit);
            
            $history = [];
            foreach ($messages as $message) {
                $history[] = [
                    'type' => $message->message_type,
                    'content' => $message->content,
                    'metadata' => $message->metadata ?? [],
                    'token_count' => $message->token_count ?? 0,
                    'created_at' => $message->created_at->format('Y-m-d H:i:s')
                ];
            }
            
            Logger::info('History retrieved from MySQL memory', [
                'session_id' => $sessionId,
                'message_count' => count($history)
            ]);
            
            return $history;
            
        } catch (Exception $e) {
            Logger::error('Failed to get history from MySQL memory', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            throw new AiServiceException("Failed to get history: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * 清除会话记忆
     * @param string $sessionId 会话ID
     * @return void
     */
    public function clearSession(string $sessionId): void
    {
        try {
            // 删除消息
            AiChatMessage::where('session_id', $sessionId)->delete();
            
            // 删除上下文
            AiChatContext::removeContext($sessionId);
            
            // 删除会话
            AiChatSession::where('session_id', $sessionId)->delete();
            
            Logger::info('Session cleared from MySQL memory', [
                'session_id' => $sessionId
            ]);
            
        } catch (Exception $e) {
            Logger::error('Failed to clear session from MySQL memory', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            throw new AiServiceException("Failed to clear session: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * 保存上下文信息
     * @param string $sessionId 会话ID
     * @param string $key 键名
     * @param mixed $value 值
     * @return void
     */
    public function saveContext(string $sessionId, string $key, $value): void
    {
        try {
            // 确保会话存在
            $this->ensureSessionExists($sessionId);
            
            // 保存上下文
            AiChatContext::setContext($sessionId, $key, $value);
            
            Logger::info('Context saved to MySQL memory', [
                'session_id' => $sessionId,
                'context_key' => $key
            ]);
            
        } catch (Exception $e) {
            Logger::error('Failed to save context to MySQL memory', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            throw new AiServiceException("Failed to save context: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * 获取上下文信息
     * @param string $sessionId 会话ID
     * @param string $key 键名
     * @return mixed
     */
    public function getContext(string $sessionId, string $key)
    {
        try {
            $value = AiChatContext::getContext($sessionId, $key);
            
            Logger::info('Context retrieved from MySQL memory', [
                'session_id' => $sessionId,
                'context_key' => $key
            ]);
            
            return $value;
            
        } catch (Exception $e) {
            Logger::error('Failed to get context from MySQL memory', [
                'session_id' => $sessionId,
                'context_key' => $key,
                'error' => $e->getMessage()
            ]);
            throw new AiServiceException("Failed to get context: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * 获取所有上下文信息（向后兼容方法）
     * @param string $sessionId 会话ID
     * @return array
     */
    public function getAllContext(string $sessionId): array
    {
        try {
            $contexts = AiChatContext::getSessionContexts($sessionId);
            
            $result = [];
            foreach ($contexts as $context) {
                $result[$context->context_key] = $context->context_value;
            }
            
            Logger::info('All context retrieved from MySQL memory', [
                'session_id' => $sessionId,
                'context_keys' => array_keys($result)
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            Logger::error('Failed to get all context from MySQL memory', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            throw new AiServiceException("Failed to get all context: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * 确保会话存在
     * @param string $sessionId 会话ID
     * @return void
     */
    protected function ensureSessionExists(string $sessionId): void
    {
        $session = AiChatSession::where('session_id', $sessionId)->find();
        
        if (!$session) {
            AiChatSession::createSession([
                'session_id' => $sessionId,
                'status' => 1,
                'last_activity' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * 更新会话活跃时间
     * @param string $sessionId 会话ID
     * @return void
     */
    protected function updateSessionActivity(string $sessionId): void
    {
        $session = AiChatSession::where('session_id', $sessionId)->find();
        if ($session) {
            $session->updateLastActivity();
            $session->incrementMessageCount();
        }
    }

    /**
     * 清理旧消息
     * @param string $sessionId 会话ID
     * @return void
     */
    protected function cleanupOldMessages(string $sessionId): void
    {
        $messageCount = AiChatMessage::where('session_id', $sessionId)->count();
        
        if ($messageCount > $this->maxHistoryLength) {
            $deleteCount = $messageCount - $this->maxHistoryLength;
            
            // 删除最旧的消息
            $oldMessages = AiChatMessage::where('session_id', $sessionId)
                ->order('created_at', 'asc')
                ->limit($deleteCount)
                ->select();
            
            foreach ($oldMessages as $message) {
                $message->delete();
            }
            
            Logger::info('Cleaned up old messages', [
                'session_id' => $sessionId,
                'deleted_count' => $deleteCount
            ]);
        }
    }
}
