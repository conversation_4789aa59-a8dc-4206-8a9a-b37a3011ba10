<?php
declare(strict_types=1);

namespace app\ai\utils;

use think\facade\Log;

/**
 * AI模块日志工具类
 */
class Logger
{
    /**
     * 记录信息日志
     * @param string $message
     * @param array $context
     */
    public static function info(string $message, array $context = []): void
    {
        Log::info(self::formatMessage($message, $context));
    }

    /**
     * 记录错误日志
     * @param string $message
     * @param array $context
     */
    public static function error(string $message, array $context = []): void
    {
        Log::error(self::formatMessage($message, $context));
    }

    /**
     * 记录警告日志
     * @param string $message
     * @param array $context
     */
    public static function warning(string $message, array $context = []): void
    {
        Log::warning(self::formatMessage($message, $context));
    }

    /**
     * 记录调试日志
     * @param string $message
     * @param array $context
     */
    public static function debug(string $message, array $context = []): void
    {
        Log::debug(self::formatMessage($message, $context));
    }

    /**
     * 格式化日志消息
     * @param string $message
     * @param array $context
     * @return string
     */
    protected static function formatMessage(string $message, array $context = []): string
    {
        $prefix = '[ai] ';
        if (!empty($context)) {
            return $prefix . $message . ' ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }
        return $prefix . $message;
    }
}