<?php
declare(strict_types=1);

namespace app\ai\utils;

/**
 * AI模块日志工具类
 * 支持ThinkPHP Log facade和独立运行模式
 */
class Logger
{
    /**
     * 记录信息日志
     * @param string $message
     * @param array $context
     */
    public static function info(string $message, array $context = []): void
    {
        self::log('info', $message, $context);
    }

    /**
     * 记录错误日志
     * @param string $message
     * @param array $context
     */
    public static function error(string $message, array $context = []): void
    {
        self::log('error', $message, $context);
    }

    /**
     * 记录警告日志
     * @param string $message
     * @param array $context
     */
    public static function warning(string $message, array $context = []): void
    {
        self::log('warning', $message, $context);
    }

    /**
     * 记录调试日志
     * @param string $message
     * @param array $context
     */
    public static function debug(string $message, array $context = []): void
    {
        self::log('debug', $message, $context);
    }

    /**
     * 统一日志记录方法
     * @param string $level
     * @param string $message
     * @param array $context
     */
    protected static function log(string $level, string $message, array $context = []): void
    {
        $formattedMessage = self::formatMessage($message, $context);

        // 尝试使用ThinkPHP Log facade
        if (class_exists('think\facade\Log')) {
            try {
                $logClass = \think\facade\Log::class;
                $logClass::$level($formattedMessage);
                return;
            } catch (\Exception $e) {
                // ThinkPHP Log不可用，使用备用方案
            }
        }

        // 备用方案：直接输出到控制台或文件
        self::fallbackLog($level, $formattedMessage);
    }

    /**
     * 备用日志记录方法
     * @param string $level
     * @param string $message
     */
    protected static function fallbackLog(string $level, string $message): void
    {
        $timestamp = date('Y-m-d H:i:s');
        $levelUpper = strtoupper($level);
        $logLine = "[{$timestamp}] [{$levelUpper}] {$message}\n";

        // 如果是CLI模式，直接输出到控制台
        if (php_sapi_name() === 'cli') {
            echo $logLine;
        } else {
            // Web模式下，尝试写入日志文件
            $logFile = sys_get_temp_dir() . '/ai_service.log';
            file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
        }
    }

    /**
     * 格式化日志消息
     * @param string $message
     * @param array $context
     * @return string
     */
    protected static function formatMessage(string $message, array $context = []): string
    {
        $prefix = '[ai] ';
        if (!empty($context)) {
            return $prefix . $message . ' ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }
        return $prefix . $message;
    }
}