<?php

namespace app\ai\utils;

use app\ai\exceptions\AiServiceException;

/**
 * 增强的HTTP客户端
 * 专门用于AI服务的HTTP请求，包含详细的错误处理和调试信息
 */
class HttpClient
{
    /**
     * 默认超时时间（秒）
     */
    private int $timeout = 30;

    /**
     * 连接超时时间（秒）
     */
    private int $connectTimeout = 10;

    /**
     * 是否启用调试模式
     */
    private bool $debug = false;

    /**
     * 最大重试次数
     */
    private int $maxRetries = 3;

    /**
     * 重试间隔（秒）
     */
    private int $retryDelay = 1;

    public function __construct(array $options = [])
    {
        $this->timeout = $options['timeout'] ?? 30;
        $this->connectTimeout = $options['connect_timeout'] ?? 10;
        $this->debug = $options['debug'] ?? false;
        $this->maxRetries = $options['max_retries'] ?? 3;
        $this->retryDelay = $options['retry_delay'] ?? 1;
    }

    /**
     * 发送POST请求
     * @param string $url
     * @param array $data
     * @param array $headers
     * @param array $options
     * @return array
     * @throws AiServiceException
     */
    public function post(string $url, array $data = [], array $headers = [], array $options = []): array
    {
        return $this->request('POST', $url, $data, $headers, $options);
    }

    /**
     * 发送GET请求
     * @param string $url
     * @param array $params
     * @param array $headers
     * @param array $options
     * @return array
     * @throws AiServiceException
     */
    public function get(string $url, array $params = [], array $headers = [], array $options = []): array
    {
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        return $this->request('GET', $url, [], $headers, $options);
    }

    /**
     * 发送HTTP请求
     * @param string $method
     * @param string $url
     * @param array $data
     * @param array $headers
     * @param array $options
     * @return array
     * @throws AiServiceException
     */
    public function request(string $method, string $url, array $data = [], array $headers = [], array $options = []): array
    {
        $attempt = 0;
        $lastError = null;

        while ($attempt < $this->maxRetries) {
            $attempt++;
            
            try {
                return $this->executeRequest($method, $url, $data, $headers, $options);
            } catch (AiServiceException $e) {
                $lastError = $e;
                
                // 如果是网络错误且还有重试机会，则重试
                if ($this->shouldRetry($e) && $attempt < $this->maxRetries) {
                    Logger::warning("HTTP request failed, retrying ({$attempt}/{$this->maxRetries})", [
                        'url' => $url,
                        'error' => $e->getMessage()
                    ]);
                    sleep($this->retryDelay);
                    continue;
                }
                
                throw $e;
            }
        }

        throw $lastError;
    }

    /**
     * 执行单次HTTP请求
     * @param string $method
     * @param string $url
     * @param array $data
     * @param array $headers
     * @param array $options
     * @return array
     * @throws AiServiceException
     */
    private function executeRequest(string $method, string $url, array $data, array $headers, array $options): array
    {
        $startTime = microtime(true);
        
        // 初始化cURL
        $ch = curl_init();
        if (!$ch) {
            throw new AiServiceException('Failed to initialize cURL');
        }

        try {
            // 设置基本选项
            $curlOptions = [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => $options['timeout'] ?? $this->timeout,
                CURLOPT_CONNECTTIMEOUT => $options['connect_timeout'] ?? $this->connectTimeout,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 3,
                CURLOPT_SSL_VERIFYPEER => $options['ssl_verify'] ?? false,
                CURLOPT_SSL_VERIFYHOST => $options['ssl_verify'] ?? false ? 2 : 0,
                CURLOPT_USERAGENT => 'AI-Service-Client/1.0',
                CURLOPT_HEADER => false,
                CURLOPT_VERBOSE => $this->debug,
            ];

            // 设置请求方法和数据
            if (strtoupper($method) === 'POST') {
                $curlOptions[CURLOPT_POST] = true;
                if (!empty($data)) {
                    $curlOptions[CURLOPT_POSTFIELDS] = json_encode($data);
                    $headers['Content-Type'] = 'application/json';
                }
            } elseif (strtoupper($method) !== 'GET') {
                $curlOptions[CURLOPT_CUSTOMREQUEST] = strtoupper($method);
                if (!empty($data)) {
                    $curlOptions[CURLOPT_POSTFIELDS] = json_encode($data);
                    $headers['Content-Type'] = 'application/json';
                }
            }

            // 设置请求头
            if (!empty($headers)) {
                $headerArray = [];
                foreach ($headers as $key => $value) {
                    $headerArray[] = "$key: $value";
                }
                $curlOptions[CURLOPT_HTTPHEADER] = $headerArray;
            }

            // 应用cURL选项
            curl_setopt_array($ch, $curlOptions);

            // 调试信息
            if ($this->debug) {
                Logger::debug('HTTP Request Debug', [
                    'method' => $method,
                    'url' => $url,
                    'headers' => $headers,
                    'data' => $data,
                    'curl_options' => $curlOptions
                ]);
            }

            Logger::info('HTTP Request Started', []);

            // 执行请求
            $response = curl_exec($ch);
            Logger::info('HTTP Request Completed: ' . $response);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            Logger::info('HTTP Request Completed: ' . $httpCode);
            $curlErrno = curl_errno($ch);
            Logger::info('HTTP Request Completed: ' . $curlErrno);
            
            // 获取详细信息
            $info = curl_getinfo($ch);
            $executionTime = (microtime(true) - $startTime) * 1000;

            curl_close($ch);

            // 检查cURL错误
            if ($response === false || $curlErrno !== 0) {
                $errorMsg = $curlError ?: 'Unknown cURL error';
                Logger::error('cURL Error', [
                    'url' => $url,
                    'error_code' => $curlErrno,
                    'error_message' => $errorMsg,
                    'info' => $info
                ]);
                throw new AiServiceException("cURL Error ({$curlErrno}): {$errorMsg}");
            }

            // 记录请求信息
            Logger::info('HTTP Request Completed', [
                'method' => $method,
                'url' => $url,
                'http_code' => $httpCode,
                'execution_time' => round($executionTime, 2) . 'ms',
                'response_size' => strlen($response)
            ]);

            // 检查HTTP状态码
            if ($httpCode < 200 || $httpCode >= 300) {
                Logger::error('HTTP Error', [
                    'url' => $url,
                    'http_code' => $httpCode,
                    'response' => substr($response, 0, 1000) // 只记录前1000字符
                ]);
                throw new AiServiceException("HTTP Error {$httpCode}: " . substr($response, 0, 200));
            }

            // 解析JSON响应
            $responseData = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Logger::error('JSON Parse Error', [
                    'url' => $url,
                    'json_error' => json_last_error_msg(),
                    'response' => substr($response, 0, 500)
                ]);
                throw new AiServiceException('Invalid JSON response: ' . json_last_error_msg());
            }

            return [
                'data' => $responseData,
                'http_code' => $httpCode,
                'execution_time' => $executionTime,
                'response_size' => strlen($response),
                'info' => $info
            ];

        } catch (AiServiceException $e) {
            curl_close($ch);
            throw $e;
        } catch (\Exception $e) {
            curl_close($ch);
            throw new AiServiceException('HTTP request failed: ' . $e->getMessage());
        }
    }

    /**
     * 判断是否应该重试
     * @param AiServiceException $e
     * @return bool
     */
    private function shouldRetry(AiServiceException $e): bool
    {
        $message = $e->getMessage();
        
        // 网络相关错误应该重试
        $retryableErrors = [
            'Connection timed out',
            'Couldn\'t connect to server',
            'Operation timed out',
            'SSL connection timeout',
            'Failed to connect',
            'Network is unreachable',
            'Connection refused'
        ];

        foreach ($retryableErrors as $error) {
            if (stripos($message, $error) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 启用调试模式
     * @return self
     */
    public function enableDebug(): self
    {
        $this->debug = true;
        return $this;
    }

    /**
     * 禁用调试模式
     * @return self
     */
    public function disableDebug(): self
    {
        $this->debug = false;
        return $this;
    }

    /**
     * 设置超时时间
     * @param int $timeout
     * @return self
     */
    public function setTimeout(int $timeout): self
    {
        $this->timeout = $timeout;
        return $this;
    }

    /**
     * 设置最大重试次数
     * @param int $maxRetries
     * @return self
     */
    public function setMaxRetries(int $maxRetries): self
    {
        $this->maxRetries = $maxRetries;
        return $this;
    }
}
