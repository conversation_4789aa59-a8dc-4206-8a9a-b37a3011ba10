<?php

namespace app\ai\services;

/**
 * 阿拉伯文分词服务
 * 参照 ChineseSegmentationService 架构设计
 */
class ArabicSegmentationService
{
    /**
     * 阿拉伯文停用词列表
     * @var array
     */
    protected array $stopWords = [
        'في', 'من', 'إلى', 'على', 'عن', 'مع', 'بعد', 'قبل', 'تحت', 'فوق', 'أمام', 'خلف', 'بين', 'ضد', 'حول',
        'أنا', 'أنت', 'هو', 'هي', 'نحن', 'أنتم', 'هم', 'هن', 'أنتِ', 'أنتما', 'هما', 'إياي', 'إياك', 'إياه', 'إياها',
        'ما', 'متى', 'أين', 'لماذا', 'كيف', 'أي', 'من', 'ماذا', 'كم', 'أين', 'متى', 'كيف',
        'من فضلك', 'شكرا', 'مرحبا', 'آسف', 'عذرا', 'أهلا', 'وداعا', 'صباح الخير', 'مساء الخير',
        'هذا', 'هذه', 'ذلك', 'تلك', 'هؤلاء', 'أولئك', 'هنا', 'هناك', 'هنالك',
        'كان', 'كانت', 'يكون', 'تكون', 'سوف', 'قد', 'لقد', 'ربما', 'إذا', 'لو', 'لكن', 'لكن', 'أو', 'أم'
    ];

    /**
     * 阿拉伯文核心业务词典
     * @var array
     */
    protected array $coreBusinessDict = [
        // 电商平台
        'أمازون' => 10, 'سوق' => 9, 'نون' => 9, 'جوميا' => 8, 'علي بابا' => 9,
        'إي باي' => 8, 'أوليكس' => 7, 'دوبيزل' => 7, 'حراج' => 7,
        
        // 支付方式
        'فيزا' => 9, 'ماستركارد' => 9, 'باي بال' => 10, 'أبل باي' => 8, 'جوجل باي' => 8,
        'بطاقة ائتمان' => 9, 'بطاقة خصم' => 7, 'تحويل بنكي' => 8, 'نقدا' => 6,
        
        // 核心业务动作
        'شراء' => 10, 'شرى' => 10, 'دفع' => 10, 'دفعة' => 9, 'شحن' => 9,
        'استرداد' => 10, 'إرجاع' => 9, 'استبدال' => 8, 'طلب' => 10, 'توصيل' => 9,
        'تسليم' => 8, 'منتج' => 9, 'سلعة' => 8, 'سعر' => 8, 'ثمن' => 7,
        'خصم' => 8, 'تخفيض' => 8, 'عرض' => 7, 'كوبون' => 7, 'نقاط' => 7,
        
        // 账户相关
        'تسجيل الدخول' => 9, 'تسجيل' => 9, 'اشتراك' => 8, 'خروج' => 7, 'حساب' => 9,
        'ملف شخصي' => 7, 'كلمة المرور' => 9, 'بريد إلكتروني' => 8, 'اسم المستخدم' => 7, 'تحقق' => 8,
        
        // 客户服务
        'دعم' => 9, 'مساعدة' => 8, 'خدمة العملاء' => 10, 'استفسار' => 8, 'محادثة' => 7,
        'هاتف' => 7, 'دعم بريد إلكتروني' => 8, 'تذكرة' => 7, 'شكوى' => 8, 'ملاحظات' => 7,
        
        // 产品类别
        'إلكترونيات' => 8, 'ملابس' => 7, 'كتب' => 7, 'منزل' => 6, 'رياضة' => 7,
        'ألعاب' => 7, 'جمال' => 7, 'صحة' => 7, 'سيارات' => 7, 'طعام' => 7
    ];

    /**
     * 阿拉伯文扩展业务词典
     * @var array
     */
    protected array $extendedBusinessDict = [
        // 购物动作
        'تصفح' => 6, 'بحث' => 7, 'فلتر' => 6, 'ترتيب' => 5, 'مقارنة' => 6,
        'قائمة الأمنيات' => 6, 'عربة التسوق' => 7, 'سلة' => 6, 'مفضلة' => 6, 'حفظ' => 5,
        
        // 订单状态
        'في الانتظار' => 6, 'قيد المعالجة' => 7, 'تم الشحن' => 7, 'تم التسليم' => 7, 'ملغى' => 7,
        'مؤكد' => 6, 'مكتمل' => 6, 'فشل' => 6, 'منتهي الصلاحية' => 5,
        
        // 产品属性
        'علامة تجارية' => 6, 'موديل' => 6, 'حجم' => 6, 'لون' => 6, 'وزن' => 5,
        'مادة' => 5, 'جودة' => 6, 'حالة' => 6, 'جديد' => 5, 'مستعمل' => 5,
        
        // 时间相关
        'اليوم' => 5, 'غدا' => 5, 'أمس' => 5, 'أسبوع' => 5, 'شهر' => 5,
        'أيام العمل' => 6, 'عطلة نهاية الأسبوع' => 5, 'عطلة' => 5, 'ساعات' => 5,
        
        // 数量和测量
        'كمية' => 6, 'مبلغ' => 6, 'إجمالي' => 6, 'المجموع الفرعي' => 6, 'ضريبة' => 6,
        'رسوم' => 6, 'تكلفة' => 6, 'معدل' => 5, 'نسبة مئوية' => 5
    ];

    /**
     * 阿拉伯文组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        'خدمة العملاء', 'بطاقة ائتمان', 'بطاقة هدايا', 'عربة التسوق', 'قائمة الأمنيات',
        'حساب المستخدم', 'عنوان البريد الإلكتروني', 'رقم الهاتف', 'الرمز البريدي', 'العنوان',
        'أيام العمل', 'ساعات العمل', 'سياسة الإرجاع', 'سياسة الخصوصية', 'شروط الخدمة',
        'شحن مجاني', 'توصيل سريع', 'توصيل في نفس اليوم', 'توصيل في اليوم التالي',
        'تسوق عبر الإنترنت', 'تطبيق الهاتف المحمول', 'متصفح الويب', 'محرك البحث', 'وسائل التواصل الاجتماعي',
        'مراجعة المنتج', 'مراجعة العميل', 'تقييم بالنجوم', 'وصف المنتج',
        'تاريخ الطلبات', 'تاريخ الشراء', 'طريقة الدفع', 'عنوان الفواتير', 'عنوان الشحن'
    ];

    /**
     * 提取阿拉伯文关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 多种分词策略
        $keywords = [];
        
        // 策略1: 组合词优先
        $compoundKeywords = $this->extractCompoundWords($cleanText);
        $keywords = array_merge($keywords, $compoundKeywords);
        
        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);
        
        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);
        
        // 策略4: 阿拉伯文词汇分词
        $wordKeywords = $this->arabicWordSegmentation($cleanText);
        $keywords = array_merge($keywords, $wordKeywords);
        
        // 策略5: 词根分析
        $rootKeywords = $this->arabicRootAnalysis($cleanText);
        $keywords = array_merge($keywords, $rootKeywords);
        
        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);
        
        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理阿拉伯文文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 规范化阿拉伯文字符
        $text = $this->normalizeArabicText($text);
        
        // 移除特殊符号，但保留阿拉伯文字符
        $text = preg_replace('/[^\p{Arabic}\p{N}\s]/u', ' ', $text);
        
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 规范化阿拉伯文文本
     * @param string $text
     * @return string
     */
    protected function normalizeArabicText(string $text): string
    {
        // 统一阿拉伯文字符变体
        $normalizations = [
            'أ' => 'ا', 'إ' => 'ا', 'آ' => 'ا', // Alif variations
            'ة' => 'ه', // Taa Marbouta to Haa
            'ى' => 'ي', // Alif Maksura to Yaa
        ];
        
        foreach ($normalizations as $from => $to) {
            $text = str_replace($from, $to, $text);
        }
        
        // 移除变音符号
        $text = preg_replace('/[\x{064B}-\x{065F}\x{0670}\x{06D6}-\x{06ED}]/u', '', $text);
        
        return $text;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->compoundWords as $compound) {
            if (strpos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->coreBusinessDict as $word => $weight) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 阿拉伯文词汇分词
     * @param string $text
     * @return array
     */
    protected function arabicWordSegmentation(string $text): array
    {
        $keywords = [];
        
        // 按空格分割（阿拉伯文有空格分隔）
        $words = preg_split('/\s+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 2 && preg_match('/[\p{Arabic}]/u', $word)) {
                $keywords[] = $word;
                
                // 去除常见前缀和后缀
                $stemmed = $this->removeArabicAffixes($word);
                if ($stemmed !== $word && mb_strlen($stemmed) >= 3) {
                    $keywords[] = $stemmed;
                }
            }
        }
        
        return $keywords;
    }

    /**
     * 阿拉伯文词根分析
     * @param string $text
     * @return array
     */
    protected function arabicRootAnalysis(string $text): array
    {
        $keywords = [];
        
        // 提取阿拉伯文词汇（2-8字符）
        preg_match_all('/[\p{Arabic}]{2,8}/u', $text, $matches);
        
        if (!empty($matches[0])) {
            foreach ($matches[0] as $word) {
                // 尝试提取三字母词根
                $root = $this->extractTriconsonantalRoot($word);
                if ($root && mb_strlen($root) === 3) {
                    $keywords[] = $root;
                }
            }
        }
        
        return $keywords;
    }

    /**
     * 去除阿拉伯文前缀和后缀
     * @param string $word
     * @return string
     */
    protected function removeArabicAffixes(string $word): string
    {
        // 常见前缀
        $prefixes = ['ال', 'و', 'ف', 'ب', 'ك', 'ل', 'لل'];
        
        // 常见后缀
        $suffixes = ['ها', 'ان', 'ات', 'ون', 'ين', 'ة', 'ه', 'ك', 'ت', 'ن'];
        
        $stemmed = $word;
        
        // 去除前缀
        foreach ($prefixes as $prefix) {
            if (mb_substr($stemmed, 0, mb_strlen($prefix)) === $prefix && mb_strlen($stemmed) > mb_strlen($prefix) + 2) {
                $stemmed = mb_substr($stemmed, mb_strlen($prefix));
                break;
            }
        }
        
        // 去除后缀
        foreach ($suffixes as $suffix) {
            if (mb_substr($stemmed, -mb_strlen($suffix)) === $suffix && mb_strlen($stemmed) > mb_strlen($suffix) + 2) {
                $stemmed = mb_substr($stemmed, 0, -mb_strlen($suffix));
                break;
            }
        }
        
        return $stemmed;
    }

    /**
     * 提取三字母词根
     * @param string $word
     * @return string|null
     */
    protected function extractTriconsonantalRoot(string $word): ?string
    {
        // 简化的三字母词根提取
        $consonants = [];
        $chars = preg_split('//u', $word, -1, PREG_SPLIT_NO_EMPTY);
        
        // 阿拉伯文辅音字母
        $consonantPattern = '/[بتثجحخدذرزسشصضطظعغفقكلمنهوي]/u';
        
        foreach ($chars as $char) {
            if (preg_match($consonantPattern, $char)) {
                $consonants[] = $char;
                if (count($consonants) >= 3) {
                    break;
                }
            }
        }
        
        return count($consonants) >= 3 ? implode('', array_slice($consonants, 0, 3)) : null;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无效词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidArabicKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateArabicWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 验证阿拉伯文关键词有效性
     * @param string $keyword
     * @return bool
     */
    protected function isValidArabicKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 15) {
            return false;
        }
        
        // 停用词检查
        if (in_array($keyword, $this->stopWords)) {
            return false;
        }
        
        // 必须包含阿拉伯文字符
        if (!preg_match('/[\p{Arabic}]/u', $keyword)) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算阿拉伯文关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateArabicWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += $length * 0.1;
        
        // 2. 频率权重
        $frequency = substr_count($text, $keyword);
        $weight += $frequency * 0.3;
        
        // 3. 业务词典权重
        if (isset($this->coreBusinessDict[$keyword])) {
            $weight += $this->coreBusinessDict[$keyword] * 0.1;
        } elseif (isset($this->extendedBusinessDict[$keyword])) {
            $weight += $this->extendedBusinessDict[$keyword] * 0.05;
        }
        
        // 4. 组合词权重
        if (in_array($keyword, $this->compoundWords)) {
            $weight += 2.0;
        }
        
        // 5. 词根权重
        if (mb_strlen($keyword) === 3 && preg_match('/^[بتثجحخدذرزسشصضطظعغفقكلمنهوي]{3}$/u', $keyword)) {
            $weight += 0.5; // 三字母词根
        }
        
        return $weight;
    }

    /**
     * 调试信息：显示阿拉伯文分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'normalized' => $this->normalizeArabicText($text),
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'word_segmentation' => $this->arabicWordSegmentation($cleanText),
            'root_analysis' => $this->arabicRootAnalysis($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
