<?php

namespace app\ai\services;

/**
 * 英文分词服务
 * 参照 ChineseSegmentationService 架构设计
 */
class EnglishSegmentationService
{
    /**
     * 英文停用词列表
     * @var array
     */
    protected array $stopWords = [
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 
        'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 
        'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall',
        'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 
        'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their',
        'what', 'where', 'when', 'why', 'how', 'which', 'who', 'whom', 'whose',
        'please', 'thank', 'thanks', 'hello', 'hi', 'sorry', 'excuse', 'welcome'
    ];

    /**
     * 英文核心业务词典
     * @var array
     */
    protected array $coreBusinessDict = [
        // E-commerce platforms
        'amazon' => 10, 'ebay' => 10, 'walmart' => 9, 'target' => 8, 'bestbuy' => 8,
        'shopify' => 8, 'etsy' => 7, 'alibaba' => 9, 'aliexpress' => 9,
        
        // Payment methods
        'paypal' => 10, 'stripe' => 9, 'visa' => 8, 'mastercard' => 8, 'amex' => 7,
        'apple pay' => 8, 'google pay' => 8, 'venmo' => 7, 'zelle' => 6,
        
        // Core business actions
        'buy' => 10, 'purchase' => 10, 'payment' => 10, 'pay' => 10, 'checkout' => 9,
        'refund' => 10, 'return' => 9, 'exchange' => 8, 'order' => 10, 'shipping' => 9,
        'delivery' => 8, 'product' => 9, 'item' => 8, 'price' => 8, 'cost' => 7,
        'discount' => 8, 'coupon' => 7, 'sale' => 8, 'deal' => 7, 'offer' => 7,
        
        // Account related
        'login' => 9, 'register' => 9, 'signup' => 9, 'signin' => 8, 'account' => 9,
        'profile' => 7, 'password' => 9, 'email' => 8, 'username' => 7, 'verification' => 8,
        
        // Customer service
        'support' => 9, 'help' => 8, 'customer service' => 10, 'contact' => 8, 'chat' => 7,
        'phone' => 7, 'email support' => 8, 'ticket' => 7, 'complaint' => 8, 'feedback' => 7,
        
        // Product categories
        'electronics' => 8, 'clothing' => 8, 'books' => 7, 'home' => 7, 'garden' => 6,
        'sports' => 7, 'toys' => 7, 'beauty' => 7, 'health' => 7, 'automotive' => 7
    ];

    /**
     * 英文扩展业务词典
     * @var array
     */
    protected array $extendedBusinessDict = [
        // Shopping actions
        'browse' => 6, 'search' => 7, 'filter' => 6, 'sort' => 5, 'compare' => 6,
        'wishlist' => 6, 'cart' => 7, 'basket' => 6, 'favorite' => 6, 'save' => 5,
        
        // Order status
        'pending' => 6, 'processing' => 7, 'shipped' => 7, 'delivered' => 7, 'cancelled' => 7,
        'confirmed' => 6, 'completed' => 6, 'failed' => 6, 'expired' => 5,
        
        // Product attributes
        'brand' => 6, 'model' => 6, 'size' => 6, 'color' => 6, 'weight' => 5,
        'material' => 5, 'quality' => 6, 'condition' => 6, 'new' => 5, 'used' => 5,
        
        // Time related
        'today' => 5, 'tomorrow' => 5, 'yesterday' => 5, 'week' => 5, 'month' => 5,
        'business days' => 6, 'weekend' => 5, 'holiday' => 5, 'hours' => 5,
        
        // Quantity and measurement
        'quantity' => 6, 'amount' => 6, 'total' => 6, 'subtotal' => 6, 'tax' => 6,
        'fee' => 6, 'charge' => 6, 'rate' => 5, 'percentage' => 5
    ];

    /**
     * 英文组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        'customer service', 'credit card', 'gift card', 'shopping cart', 'wish list',
        'user account', 'email address', 'phone number', 'zip code', 'postal code',
        'business days', 'working hours', 'return policy', 'privacy policy', 'terms of service',
        'free shipping', 'express delivery', 'same day delivery', 'next day delivery',
        'online shopping', 'mobile app', 'web browser', 'search engine', 'social media',
        'product review', 'customer review', 'star rating', 'product description',
        'order history', 'purchase history', 'payment method', 'billing address', 'shipping address'
    ];

    /**
     * 提取英文关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 多种分词策略
        $keywords = [];
        
        // 策略1: 组合词优先
        $compoundKeywords = $this->extractCompoundWords($cleanText);
        $keywords = array_merge($keywords, $compoundKeywords);
        
        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);
        
        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);
        
        // 策略4: 标准英文分词
        $standardKeywords = $this->standardEnglishSegmentation($cleanText);
        $keywords = array_merge($keywords, $standardKeywords);
        
        // 策略5: 词根和变形处理
        $stemmedKeywords = $this->stemAndVariationProcessing($cleanText);
        $keywords = array_merge($keywords, $stemmedKeywords);
        
        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);
        
        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理英文文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 转换为小写
        $text = mb_strtolower($text);
        
        // 处理常见缩写
        $contractions = [
            "don't" => "do not", "won't" => "will not", "can't" => "cannot",
            "isn't" => "is not", "aren't" => "are not", "wasn't" => "was not",
            "weren't" => "were not", "haven't" => "have not", "hasn't" => "has not",
            "hadn't" => "had not", "wouldn't" => "would not", "couldn't" => "could not",
            "shouldn't" => "should not", "mustn't" => "must not", "needn't" => "need not",
            "i'm" => "i am", "you're" => "you are", "he's" => "he is", "she's" => "she is",
            "it's" => "it is", "we're" => "we are", "they're" => "they are",
            "i've" => "i have", "you've" => "you have", "we've" => "we have", "they've" => "they have",
            "i'll" => "i will", "you'll" => "you will", "he'll" => "he will", "she'll" => "she will",
            "we'll" => "we will", "they'll" => "they will"
        ];
        
        foreach ($contractions as $contraction => $expansion) {
            $text = str_replace($contraction, $expansion, $text);
        }
        
        // 移除特殊字符，保留字母、数字、空格和连字符
        $text = preg_replace('/[^\p{L}\p{N}\s\-]/u', ' ', $text);
        
        // 规范化空格
        $text = preg_replace('/\s+/', ' ', trim($text));
        
        return $text;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->compoundWords as $compound) {
            if (stripos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->coreBusinessDict as $word => $weight) {
            if (stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 标准英文分词
     * @param string $text
     * @return array
     */
    protected function standardEnglishSegmentation(string $text): array
    {
        $keywords = [];
        
        // 按空格和标点分割
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (strlen($word) >= 2 && preg_match('/^[a-zA-Z]+$/', $word)) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 词根和变形处理
     * @param string $text
     * @return array
     */
    protected function stemAndVariationProcessing(string $text): array
    {
        $keywords = [];
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (strlen($word) >= 3) {
                // 简单的词根提取（去除常见后缀）
                $stemmed = $this->simpleStem($word);
                if ($stemmed !== $word && strlen($stemmed) >= 3) {
                    $keywords[] = $stemmed;
                }
            }
        }
        
        return $keywords;
    }

    /**
     * 简单词根提取
     * @param string $word
     * @return string
     */
    protected function simpleStem(string $word): string
    {
        // 去除常见后缀
        $suffixes = ['ing', 'ed', 'er', 'est', 'ly', 'tion', 'sion', 'ness', 'ment', 'able', 'ible', 'ful', 'less'];
        
        foreach ($suffixes as $suffix) {
            if (strlen($word) > strlen($suffix) + 2 && substr($word, -strlen($suffix)) === $suffix) {
                return substr($word, 0, -strlen($suffix));
            }
        }
        
        // 去除复数形式
        if (strlen($word) > 3 && substr($word, -1) === 's') {
            return substr($word, 0, -1);
        }
        
        return $word;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无效词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidEnglishKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateEnglishWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 验证英文关键词有效性
     * @param string $keyword
     * @return bool
     */
    protected function isValidEnglishKeyword(string $keyword): bool
    {
        // 长度检查
        $length = strlen($keyword);
        if ($length < 2 || $length > 20) {
            return false;
        }
        
        // 停用词检查
        if (in_array(strtolower($keyword), $this->stopWords)) {
            return false;
        }
        
        // 纯数字检查
        if (is_numeric($keyword) && $length < 3) {
            return false;
        }
        
        // 必须包含字母
        if (!preg_match('/[a-zA-Z]/', $keyword)) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算英文关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateEnglishWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 基础权重（长度）
        $length = strlen($keyword);
        $weight += ($length - 1) * 0.1;
        
        // 2. 频率权重
        $frequency = substr_count(strtolower($text), strtolower($keyword));
        $weight += $frequency * 0.3;
        
        // 3. 业务词典权重
        if (isset($this->coreBusinessDict[strtolower($keyword)])) {
            $weight += $this->coreBusinessDict[strtolower($keyword)] * 0.1;
        } elseif (isset($this->extendedBusinessDict[strtolower($keyword)])) {
            $weight += $this->extendedBusinessDict[strtolower($keyword)] * 0.05;
        }
        
        // 4. 组合词权重
        if (in_array(strtolower($keyword), $this->compoundWords)) {
            $weight += 2.0;
        }
        
        // 5. 位置权重（出现在前面的权重更高）
        $position = stripos($text, $keyword);
        if ($position !== false) {
            $textLength = strlen($text);
            $positionWeight = 1 - ($position / $textLength);
            $weight += $positionWeight * 0.2;
        }
        
        // 6. 完整词汇权重
        if ($this->isCompleteEnglishWord($keyword)) {
            $weight += 0.3;
        }
        
        return $weight;
    }

    /**
     * 判断是否为完整英文词汇
     * @param string $word
     * @return bool
     */
    protected function isCompleteEnglishWord(string $word): bool
    {
        $completeWords = [
            'buy', 'purchase', 'payment', 'refund', 'order', 'product', 'price', 'shipping',
            'delivery', 'account', 'login', 'register', 'password', 'support', 'help',
            'customer', 'service', 'contact', 'email', 'phone', 'address', 'cart', 'checkout'
        ];
        
        return in_array(strtolower($word), $completeWords);
    }

    /**
     * 调试信息：显示英文分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'standard_segmentation' => $this->standardEnglishSegmentation($cleanText),
            'stemmed_words' => $this->stemAndVariationProcessing($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
