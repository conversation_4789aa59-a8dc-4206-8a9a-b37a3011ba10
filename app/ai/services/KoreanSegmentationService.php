<?php

namespace app\ai\services;

/**
 * 韩文分词服务
 * 参照 ChineseSegmentationService 架构设计
 */
class KoreanSegmentationService
{
    /**
     * 韩文停用词列表
     * @var array
     */
    protected array $stopWords = [
        '이', '그', '저', '의', '를', '을', '가', '이', '에', '에서', '로', '으로', '와', '과', '하고', 
        '도', '만', '까지', '부터', '에게', '한테', '께', '에게서', '한테서', '께서',
        '나', '너', '그', '그녀', '우리', '그들', '저', '저희', '자신', '서로',
        '무엇', '언제', '어디', '왜', '어떻게', '어느', '누구', '얼마', '몇',
        '제발', '감사', '안녕', '죄송', '미안', '네', '아니오', '그렇습니다', '안녕하세요'
    ];

    /**
     * 韩文核心业务词典
     * @var array
     */
    protected array $coreBusinessDict = [
        // 电商平台
        '네이버' => 10, '쿠팡' => 10, '11번가' => 9, '지마켓' => 9, '옥션' => 8,
        '롯데온' => 8, '위메프' => 8, '티몬' => 8, '인터파크' => 7, '홈플러스' => 7,
        
        // 支付方式
        '카카오페이' => 10, '토스' => 9, '페이코' => 8, '삼성페이' => 8, '엘지페이' => 7,
        '신용카드' => 9, '체크카드' => 7, '계좌이체' => 7, '무통장입금' => 7, '포인트' => 8,
        
        // 核心业务动作
        '구매' => 10, '사다' => 10, '결제' => 10, '지불' => 9, '충전' => 9,
        '환불' => 10, '반품' => 9, '교환' => 8, '주문' => 10, '배송' => 9,
        '배달' => 8, '상품' => 9, '제품' => 8, '가격' => 8, '값' => 7,
        '할인' => 8, '세일' => 8, '쿠폰' => 7, '이벤트' => 7, '적립' => 7,
        
        // 账户相关
        '로그인' => 9, '가입' => 9, '회원가입' => 9, '로그아웃' => 8, '계정' => 9,
        '프로필' => 7, '비밀번호' => 9, '이메일' => 8, '사용자명' => 7, '인증' => 8,
        
        // 客户服务
        '고객센터' => 10, '도움말' => 8, '고객서비스' => 10, '문의' => 8, '채팅' => 7,
        '전화' => 7, '이메일지원' => 8, '티켓' => 7, '불만' => 8, '피드백' => 7,
        
        // 产品类别
        '전자제품' => 8, '의류' => 7, '도서' => 7, '가정용품' => 7, '스포츠' => 7,
        '장난감' => 7, '뷰티' => 7, '건강' => 7, '자동차' => 7, '식품' => 7
    ];

    /**
     * 韩文扩展业务词典
     * @var array
     */
    protected array $extendedBusinessDict = [
        // 购物动作
        '검색' => 7, '찾기' => 6, '필터' => 6, '정렬' => 5, '비교' => 6,
        '위시리스트' => 6, '장바구니' => 7, '바스켓' => 6, '즐겨찾기' => 6, '저장' => 5,
        
        // 订单状态
        '대기중' => 6, '처리중' => 7, '발송완료' => 7, '배송완료' => 7, '취소' => 7,
        '확인완료' => 6, '완료' => 6, '실패' => 6, '만료' => 5,
        
        // 产品属性
        '브랜드' => 6, '모델' => 6, '사이즈' => 6, '색상' => 6, '무게' => 5,
        '재료' => 5, '품질' => 6, '상태' => 6, '신품' => 5, '중고' => 5,
        
        // 时间相关
        '오늘' => 5, '내일' => 5, '어제' => 5, '주' => 5, '월' => 5,
        '영업일' => 6, '주말' => 5, '공휴일' => 5, '시간' => 5,
        
        // 数量和测量
        '수량' => 6, '금액' => 6, '총액' => 6, '소계' => 6, '세금' => 6,
        '수수료' => 6, '요금' => 6, '비율' => 5, '퍼센트' => 5
    ];

    /**
     * 韩文组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        '고객서비스', '신용카드', '기프트카드', '장바구니', '위시리스트',
        '사용자계정', '이메일주소', '전화번호', '우편번호', '주소',
        '영업일', '영업시간', '반품정책', '개인정보보호정책', '이용약관',
        '무료배송', '빠른배송', '당일배송', '익일배송', '온라인쇼핑',
        '모바일앱', '웹브라우저', '검색엔진', '소셜미디어',
        '상품리뷰', '고객리뷰', '별점', '상품설명',
        '주문내역', '구매내역', '결제방법', '청구지주소', '배송지주소'
    ];

    /**
     * 提取韩文关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 多种分词策略
        $keywords = [];
        
        // 策略1: 组合词优先
        $compoundKeywords = $this->extractCompoundWords($cleanText);
        $keywords = array_merge($keywords, $compoundKeywords);
        
        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);
        
        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);
        
        // 策略4: 韩文字符分词
        $characterKeywords = $this->koreanCharacterSegmentation($cleanText);
        $keywords = array_merge($keywords, $characterKeywords);
        
        // 策略5: 韩文语法分词
        $grammarKeywords = $this->koreanGrammarSegmentation($cleanText);
        $keywords = array_merge($keywords, $grammarKeywords);
        
        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);
        
        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理韩文文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        // 移除特殊符号，但保留韩文字符
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $text);
        
        // 再次规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->compoundWords as $compound) {
            if (strpos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->coreBusinessDict as $word => $weight) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 韩文字符分词
     * @param string $text
     * @return array
     */
    protected function koreanCharacterSegmentation(string $text): array
    {
        $keywords = [];
        
        // 1. 提取韩文音节（1-4字符）
        preg_match_all('/[\x{ac00}-\x{d7af}]{1,4}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        // 2. 提取韩文字母组合
        preg_match_all('/[\x{1100}-\x{11ff}\x{3130}-\x{318f}]{2,6}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        // 3. 提取英文单词
        preg_match_all('/[a-zA-Z]{2,}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        // 4. 提取数字
        preg_match_all('/\d{2,}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        return $keywords;
    }

    /**
     * 韩文语法分词
     * @param string $text
     * @return array
     */
    protected function koreanGrammarSegmentation(string $text): array
    {
        $keywords = [];
        
        // 按空格分割（韩文有空格分隔）
        $words = preg_split('/\s+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 1 && preg_match('/[\x{ac00}-\x{d7af}]/', $word)) {
                // 去除常见语法后缀
                $stemmed = $this->removeKoreanSuffixes($word);
                if ($stemmed !== $word && mb_strlen($stemmed) >= 2) {
                    $keywords[] = $stemmed;
                }
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 去除韩文语法后缀
     * @param string $word
     * @return string
     */
    protected function removeKoreanSuffixes(string $word): string
    {
        // 常见的韩文语法后缀
        $suffixes = ['하다', '되다', '이다', '있다', '없다', '하는', '되는', '하고', '하지', '에서', '에게', '으로', '로'];
        
        foreach ($suffixes as $suffix) {
            if (mb_strlen($word) > mb_strlen($suffix) + 1 && mb_substr($word, -mb_strlen($suffix)) === $suffix) {
                return mb_substr($word, 0, -mb_strlen($suffix));
            }
        }
        
        return $word;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无效词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidKoreanKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateKoreanWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 验证韩文关键词有效性
     * @param string $keyword
     * @return bool
     */
    protected function isValidKoreanKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 1 || $length > 10) {
            return false;
        }
        
        // 停用词检查
        if (in_array($keyword, $this->stopWords)) {
            return false;
        }
        
        // 纯空格检查
        if (trim($keyword) === '') {
            return false;
        }
        
        return true;
    }

    /**
     * 计算韩文关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateKoreanWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += $length * 0.1;
        
        // 2. 频率权重
        $frequency = substr_count($text, $keyword);
        $weight += $frequency * 0.3;
        
        // 3. 业务词典权重
        if (isset($this->coreBusinessDict[$keyword])) {
            $weight += $this->coreBusinessDict[$keyword] * 0.1;
        } elseif (isset($this->extendedBusinessDict[$keyword])) {
            $weight += $this->extendedBusinessDict[$keyword] * 0.05;
        }
        
        // 4. 组合词权重
        if (in_array($keyword, $this->compoundWords)) {
            $weight += 2.0;
        }
        
        // 5. 韩文字符权重
        if (preg_match('/[\x{ac00}-\x{d7af}]/', $keyword)) {
            $weight += 0.3; // 韩文音节权重
        }
        
        // 6. 业务相关性权重
        if ($this->hasKoreanBusinessRelevance($keyword)) {
            $weight += 0.5;
        }
        
        return $weight;
    }

    /**
     * 判断是否有韩文业务相关性
     * @param string $text
     * @return bool
     */
    protected function hasKoreanBusinessRelevance(string $text): bool
    {
        // 检查是否包含业务相关字符
        $businessChars = ['구', '매', '결', '제', '지', '불', '주', '문', '배', '송', '상', '품', 
                         '로', '그', '인', '가', '입', '고', '객', '센', '터', '도', '움', '말'];
        
        foreach ($businessChars as $char) {
            if (strpos($text, $char) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 调试信息：显示韩文分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'character_segmentation' => $this->koreanCharacterSegmentation($cleanText),
            'grammar_segmentation' => $this->koreanGrammarSegmentation($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
