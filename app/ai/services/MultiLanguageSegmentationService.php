<?php

namespace app\ai\services;

/**
 * 多语言分词服务 - 统一分发器
 * 使用专门的分词服务处理不同语言，统一分发和管理
 */
class MultiLanguageSegmentationService
{
    /**
     * 专门的分词服务实例
     * @var array
     */
    protected array $segmentationServices = [];

    /**
     * 支持的语言列表
     * @var array
     */
    protected array $supportedLanguages = [
        'zh' => '中文',
        'en' => 'English',
        'ja' => '日本語',
        'ko' => '한국어',
        'ar' => 'العربية',
        'ru' => 'Русский',
        'es' => 'Español',
        'fr' => 'Français',
        'de' => 'Deutsch',
        'pt' => 'Português',
        'it' => 'Italiano',
        'th' => 'ไทย',
        'vi' => 'Tiếng Việt',
        'hi' => 'हिन्दी'
    ];

    /**
     * 语言到分词服务的映射
     * @var array
     */
    protected array $serviceMapping = [
        // 专门分词服务
        'zh' => ChineseSegmentationService::class,
        'en' => EnglishSegmentationService::class,
        'ja' => JapaneseSegmentationService::class,
        'ko' => KoreanSegmentationService::class,
        'ar' => ArabicSegmentationService::class,
        'ru' => RussianSegmentationService::class,
        'es' => SpanishSegmentationService::class,
        'fr' => FrenchSegmentationService::class,
        'de' => GermanSegmentationService::class,
        'th' => ThaiSegmentationService::class,
        'vi' => VietnameseSegmentationService::class,
        'hi' => HindiSegmentationService::class,
        // 其他语言使用通用分词
        'pt' => 'universal',
        'it' => 'universal'
    ];

    /**
     * 多语言停用词
     * @var array
     */
    protected array $multiLanguageStopWords = [
        'zh' => [
            '的', '了', '在', '是', '我', '你', '他', '她', '它', '们', '这', '那', '有', '没', '不', '要', '会', '能', '可以',
            '怎么', '什么', '为什么', '如何', '哪里', '哪个', '什么时候', '多少', '几个', '请', '请问', '您好', '你好', '谢谢'
        ],
        'en' => [
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were',
            'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them',
            'what', 'where', 'when', 'why', 'how', 'which', 'who', 'whom', 'whose', 'please', 'thank', 'thanks', 'hello', 'hi'
        ],
        'ja' => [
            'の', 'に', 'は', 'を', 'が', 'で', 'と', 'から', 'まで', 'より', 'へ', 'や', 'か', 'も', 'だ', 'である', 'です', 'ます',
            'これ', 'それ', 'あれ', 'どれ', 'ここ', 'そこ', 'あそこ', 'どこ', '私', 'あなた', '彼', '彼女', 'われわれ', '彼ら',
            'なに', 'なん', 'いつ', 'どう', 'なぜ', 'どのように', 'お願い', 'ありがとう', 'こんにちは', 'すみません'
        ],
        'ko' => [
            '이', '그', '저', '의', '를', '을', '가', '이', '에', '에서', '로', '으로', '와', '과', '하고', '도', '만', '까지', '부터',
            '나', '너', '그', '그녀', '우리', '그들', '무엇', '언제', '어디', '왜', '어떻게', '어느', '누구', '제발', '감사', '안녕', '죄송'
        ],
        'ar' => [
            'في', 'من', 'إلى', 'على', 'عن', 'مع', 'بعد', 'قبل', 'تحت', 'فوق', 'أمام', 'خلف', 'بين', 'ضد', 'حول',
            'أنا', 'أنت', 'هو', 'هي', 'نحن', 'أنتم', 'هم', 'هن', 'ما', 'متى', 'أين', 'لماذا', 'كيف', 'أي', 'من',
            'من فضلك', 'شكرا', 'مرحبا', 'آسف', 'عذرا'
        ],
        'ru' => [
            'в', 'на', 'с', 'по', 'для', 'от', 'до', 'за', 'под', 'над', 'между', 'через', 'без', 'при', 'о', 'об',
            'я', 'ты', 'он', 'она', 'мы', 'вы', 'они', 'что', 'когда', 'где', 'почему', 'как', 'какой', 'кто',
            'пожалуйста', 'спасибо', 'привет', 'извините', 'простите'
        ],
        'es' => [
            'el', 'la', 'los', 'las', 'un', 'una', 'y', 'o', 'pero', 'en', 'de', 'a', 'por', 'para', 'con', 'sin', 'sobre',
            'yo', 'tú', 'él', 'ella', 'nosotros', 'vosotros', 'ellos', 'ellas', 'qué', 'cuándo', 'dónde', 'por qué', 'cómo',
            'por favor', 'gracias', 'hola', 'perdón', 'disculpe'
        ],
        'fr' => [
            'le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'mais', 'dans', 'de', 'à', 'pour', 'par', 'avec', 'sans', 'sur',
            'je', 'tu', 'il', 'elle', 'nous', 'vous', 'ils', 'elles', 'que', 'quand', 'où', 'pourquoi', 'comment',
            's\'il vous plaît', 'merci', 'bonjour', 'pardon', 'excusez-moi'
        ],
        'de' => [
            'der', 'die', 'das', 'ein', 'eine', 'und', 'oder', 'aber', 'in', 'von', 'zu', 'für', 'mit', 'ohne', 'über', 'unter',
            'ich', 'du', 'er', 'sie', 'wir', 'ihr', 'sie', 'was', 'wann', 'wo', 'warum', 'wie', 'welcher',
            'bitte', 'danke', 'hallo', 'entschuldigung', 'verzeihung'
        ]
    ];

    /**
     * 多语言业务词典
     * @var array
     */
    protected array $multiLanguageBusinessDict = [
        'zh' => [
            '购买' => 10, '支付' => 10, '充值' => 10, '退款' => 10, '订单' => 10, '商品' => 9, '价格' => 8,
            '淘宝' => 10, '天猫' => 10, '京东' => 10, '拼多多' => 10, '支付宝' => 10, '微信支付' => 10,
            '登录' => 9, '注册' => 9, '密码' => 9, '账户' => 9, '客服' => 9, '帮助' => 8, '测试' => 8, '功能' => 8
        ],
        'en' => [
            'buy' => 10, 'purchase' => 10, 'payment' => 10, 'pay' => 10, 'refund' => 10, 'order' => 10, 'product' => 9, 'price' => 8,
            'amazon' => 10, 'ebay' => 10, 'paypal' => 10, 'stripe' => 9, 'visa' => 8, 'mastercard' => 8,
            'login' => 9, 'register' => 9, 'signup' => 9, 'password' => 9, 'account' => 9, 'support' => 9, 'help' => 8, 'test' => 8
        ],
        'ja' => [
            '購入' => 10, '支払い' => 10, 'チャージ' => 10, '返金' => 10, '注文' => 10, '商品' => 9, '価格' => 8,
            '楽天' => 10, 'アマゾン' => 10, 'ヤフー' => 9, 'ペイペイ' => 9, 'ライン' => 8,
            'ログイン' => 9, '登録' => 9, 'パスワード' => 9, 'アカウント' => 9, 'サポート' => 9, 'ヘルプ' => 8, 'テスト' => 8
        ],
        'ko' => [
            '구매' => 10, '결제' => 10, '충전' => 10, '환불' => 10, '주문' => 10, '상품' => 9, '가격' => 8,
            '네이버' => 10, '쿠팡' => 10, '11번가' => 9, '카카오페이' => 9, '토스' => 8,
            '로그인' => 9, '가입' => 9, '비밀번호' => 9, '계정' => 9, '고객센터' => 9, '도움말' => 8, '테스트' => 8
        ],
        'ar' => [
            'شراء' => 10, 'دفع' => 10, 'شحن' => 10, 'استرداد' => 10, 'طلب' => 10, 'منتج' => 9, 'سعر' => 8,
            'أمازون' => 10, 'سوق' => 9, 'نون' => 9, 'جوميا' => 8,
            'تسجيل الدخول' => 9, 'تسجيل' => 9, 'كلمة المرور' => 9, 'حساب' => 9, 'دعم' => 9, 'مساعدة' => 8, 'اختبار' => 8
        ],
        'ru' => [
            'покупка' => 10, 'платеж' => 10, 'пополнение' => 10, 'возврат' => 10, 'заказ' => 10, 'товар' => 9, 'цена' => 8,
            'озон' => 10, 'вайлдберриз' => 10, 'яндекс' => 9, 'сбербанк' => 8,
            'вход' => 9, 'регистрация' => 9, 'пароль' => 9, 'аккаунт' => 9, 'поддержка' => 9, 'помощь' => 8, 'тест' => 8
        ],
        'es' => [
            'comprar' => 10, 'pago' => 10, 'recarga' => 10, 'reembolso' => 10, 'pedido' => 10, 'producto' => 9, 'precio' => 8,
            'amazon' => 10, 'mercadolibre' => 10, 'paypal' => 9, 'visa' => 8,
            'iniciar sesión' => 9, 'registro' => 9, 'contraseña' => 9, 'cuenta' => 9, 'soporte' => 9, 'ayuda' => 8, 'prueba' => 8
        ],
        'fr' => [
            'acheter' => 10, 'paiement' => 10, 'recharge' => 10, 'remboursement' => 10, 'commande' => 10, 'produit' => 9, 'prix' => 8,
            'amazon' => 10, 'cdiscount' => 9, 'fnac' => 8, 'paypal' => 9,
            'connexion' => 9, 'inscription' => 9, 'mot de passe' => 9, 'compte' => 9, 'support' => 9, 'aide' => 8, 'test' => 8
        ],
        'de' => [
            'kaufen' => 10, 'zahlung' => 10, 'aufladen' => 10, 'rückerstattung' => 10, 'bestellung' => 10, 'produkt' => 9, 'preis' => 8,
            'amazon' => 10, 'otto' => 9, 'zalando' => 8, 'paypal' => 9,
            'anmelden' => 9, 'registrierung' => 9, 'passwort' => 9, 'konto' => 9, 'support' => 9, 'hilfe' => 8, 'test' => 8
        ]
    ];

    /**
     * 语言检测正则表达式
     * @var array
     */
    protected array $languagePatterns = [
        'zh' => '/[\x{4e00}-\x{9fff}]/u',           // 中文字符
        'ja' => '/[\x{3040}-\x{309f}\x{30a0}-\x{30ff}\x{4e00}-\x{9fff}]/u', // 日文字符
        'ko' => '/[\x{ac00}-\x{d7af}\x{1100}-\x{11ff}\x{3130}-\x{318f}]/u', // 韩文字符
        'ar' => '/[\x{0600}-\x{06ff}\x{0750}-\x{077f}]/u', // 阿拉伯文字符
        'ru' => '/[\x{0400}-\x{04ff}]/u',           // 俄文字符
        'th' => '/[\x{0e00}-\x{0e7f}]/u',           // 泰文字符
        'hi' => '/[\x{0900}-\x{097f}]/u',           // 印地文字符
        'en' => '/^[a-zA-Z\s\d\p{P}]+$/u'           // 英文字符（最后检测）
    ];

    /**
     * 构造函数 - 初始化分词服务
     */
    public function __construct()
    {
        // 延迟加载分词服务
    }

    /**
     * 提取多语言关键词（统一分发入口）
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 检测语言
        $detectedLanguages = $this->detectLanguages($text);
        $primaryLanguage = array_key_first($detectedLanguages);
        $primaryConfidence = $detectedLanguages[$primaryLanguage] ?? 0;

        // 2. 根据主要语言选择分词策略
        if ($primaryConfidence > 0.3) {
            // 使用专门的分词服务
            $keywords = $this->segmentWithSpecializedService($text, $primaryLanguage, $maxWords);

            // 如果专门服务返回的关键词不足，补充通用分词
            if (count($keywords) < $maxWords * 0.7) {
                $universalKeywords = $this->universalSegmentation($text);
                $keywords = array_merge($keywords, $universalKeywords);
                $keywords = array_unique($keywords);
                $keywords = array_slice($keywords, 0, $maxWords);
            }
        } else {
            // 混合语言或未知语言，使用通用分词
            $keywords = $this->universalSegmentation($text);
            $keywords = array_slice($keywords, 0, $maxWords);
        }

        return $keywords;
    }

    /**
     * 使用专门的分词服务
     * @param string $text
     * @param string $language
     * @param int $maxWords
     * @return array
     */
    protected function segmentWithSpecializedService(string $text, string $language, int $maxWords): array
    {
        // 获取对应的分词服务
        $service = $this->getSegmentationService($language);

        if ($service) {
            return $service->extractKeywords($text, $maxWords);
        }

        // 降级到通用分词
        return $this->universalSegmentation($text);
    }

    /**
     * 获取指定语言的分词服务
     * @param string $language
     * @return object|null
     */
    protected function getSegmentationService(string $language)
    {
        // 如果已经实例化，直接返回
        if (isset($this->segmentationServices[$language])) {
            return $this->segmentationServices[$language];
        }

        // 检查是否有专门的分词服务
        if (!isset($this->serviceMapping[$language])) {
            return null;
        }

        $serviceClass = $this->serviceMapping[$language];

        // 如果是通用分词，返回null
        if ($serviceClass === 'universal') {
            return null;
        }

        // 实例化专门的分词服务
        try {
            $this->segmentationServices[$language] = new $serviceClass();
            return $this->segmentationServices[$language];
        } catch (\Exception $e) {
            // 实例化失败，记录错误
            error_log("Failed to instantiate segmentation service for {$language}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 检测文本中的语言
     * @param string $text
     * @return array 语言代码 => 置信度
     */
    public function detectLanguages(string $text): array
    {
        $languages = [];
        $totalChars = mb_strlen($text);
        
        if ($totalChars === 0) {
            return $languages;
        }
        
        foreach ($this->languagePatterns as $lang => $pattern) {
            preg_match_all($pattern, $text, $matches);
            $matchCount = count($matches[0]);
            $confidence = $matchCount / $totalChars;
            
            if ($confidence > 0) {
                $languages[$lang] = $confidence;
            }
        }
        
        // 如果没有检测到特定语言，默认为英文
        if (empty($languages)) {
            $languages['en'] = 1.0;
        }
        
        // 按置信度排序
        arsort($languages);
        
        return $languages;
    }

    /**
     * 预处理多语言文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        // 移除特殊符号，但保留语言特定字符
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $text);
        
        // 再次规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 根据语言进行分词
     * @param string $text
     * @param string $language
     * @return array
     */
    protected function segmentByLanguage(string $text, string $language): array
    {
        switch ($language) {
            case 'zh':
                return $this->chineseSegmentation($text);
            case 'ja':
                return $this->japaneseSegmentation($text);
            case 'ko':
                return $this->koreanSegmentation($text);
            case 'ar':
                return $this->arabicSegmentation($text);
            case 'ru':
                return $this->russianSegmentation($text);
            case 'th':
                return $this->thaiSegmentation($text);
            case 'en':
            case 'es':
            case 'fr':
            case 'de':
            case 'pt':
            case 'it':
                return $this->westernLanguageSegmentation($text, $language);
            default:
                return $this->universalSegmentation($text);
        }
    }

    /**
     * 中文分词
     * @param string $text
     * @return array
     */
    protected function chineseSegmentation(string $text): array
    {
        $keywords = [];

        // 1. 业务词典匹配
        if (isset($this->multiLanguageBusinessDict['zh'])) {
            foreach ($this->multiLanguageBusinessDict['zh'] as $word => $weight) {
                if (strpos($text, $word) !== false) {
                    $keywords[] = $word;
                }
            }
        }

        // 2. 中文词汇提取（2-4字符）
        preg_match_all('/[\x{4e00}-\x{9fff}]{2,4}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }

        // 3. 中文N-gram（2-3字符）
        $length = mb_strlen($text);
        for ($i = 0; $i < $length - 1; $i++) {
            $char = mb_substr($text, $i, 1);
            if (preg_match('/[\x{4e00}-\x{9fff}]/u', $char)) {
                // 2-gram
                if ($i < $length - 1) {
                    $gram = mb_substr($text, $i, 2);
                    if (preg_match('/^[\x{4e00}-\x{9fff}]{2}$/u', $gram)) {
                        $keywords[] = $gram;
                    }
                }
            }
        }

        return $keywords;
    }

    /**
     * 西方语言分词（英文、西班牙文、法文、德文等）
     * @param string $text
     * @param string $language
     * @return array
     */
    protected function westernLanguageSegmentation(string $text, string $language): array
    {
        $keywords = [];

        // 1. 业务词典匹配
        if (isset($this->multiLanguageBusinessDict[$language])) {
            foreach ($this->multiLanguageBusinessDict[$language] as $word => $weight) {
                if (mb_stripos($text, $word) !== false) {
                    $keywords[] = $word;
                }
            }
        }

        // 2. 按空格和标点分割
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        foreach ($words as $word) {
            $word = mb_strtolower(trim($word));
            if (mb_strlen($word) >= 2 && preg_match('/^[a-zA-Z]+$/u', $word)) {
                $keywords[] = $word;
            }
        }

        return $keywords;
    }

    /**
     * 日文分词
     * @param string $text
     * @return array
     */
    protected function japaneseSegmentation(string $text): array
    {
        $keywords = [];

        // 1. 业务词典匹配
        if (isset($this->multiLanguageBusinessDict['ja'])) {
            foreach ($this->multiLanguageBusinessDict['ja'] as $word => $weight) {
                if (strpos($text, $word) !== false) {
                    $keywords[] = $word;
                }
            }
        }

        // 2. 片假名词汇（2-6字符）
        preg_match_all('/[\x{30a0}-\x{30ff}]{2,6}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }

        // 3. 汉字词汇（1-4字符）
        preg_match_all('/[\x{4e00}-\x{9fff}]{1,4}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }

        return $keywords;
    }

    /**
     * 韩文分词
     * @param string $text
     * @return array
     */
    protected function koreanSegmentation(string $text): array
    {
        $keywords = [];

        // 1. 业务词典匹配
        if (isset($this->multiLanguageBusinessDict['ko'])) {
            foreach ($this->multiLanguageBusinessDict['ko'] as $word => $weight) {
                if (strpos($text, $word) !== false) {
                    $keywords[] = $word;
                }
            }
        }

        // 2. 韩文词汇提取（1-4字符）
        preg_match_all('/[\x{ac00}-\x{d7af}]{1,4}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }

        return $keywords;
    }

    /**
     * 阿拉伯文分词
     * @param string $text
     * @return array
     */
    protected function arabicSegmentation(string $text): array
    {
        $keywords = [];

        // 1. 业务词典匹配
        if (isset($this->multiLanguageBusinessDict['ar'])) {
            foreach ($this->multiLanguageBusinessDict['ar'] as $word => $weight) {
                if (strpos($text, $word) !== false) {
                    $keywords[] = $word;
                }
            }
        }

        // 2. 按空格分割（阿拉伯文有空格分隔）
        $words = preg_split('/\s+/u', $text);
        foreach ($words as $word) {
            if (preg_match('/[\x{0600}-\x{06ff}\x{0750}-\x{077f}]/u', $word) && mb_strlen($word) >= 2) {
                $keywords[] = $word;
            }
        }

        return $keywords;
    }

    /**
     * 俄文分词
     * @param string $text
     * @return array
     */
    protected function russianSegmentation(string $text): array
    {
        $keywords = [];

        // 1. 业务词典匹配
        if (isset($this->multiLanguageBusinessDict['ru'])) {
            foreach ($this->multiLanguageBusinessDict['ru'] as $word => $weight) {
                if (mb_stripos($text, $word) !== false) {
                    $keywords[] = $word;
                }
            }
        }

        // 2. 按空格分割
        $words = preg_split('/\s+/u', $text);
        foreach ($words as $word) {
            if (preg_match('/[\x{0400}-\x{04ff}]/u', $word) && mb_strlen($word) >= 3) {
                $keywords[] = mb_strtolower($word);
            }
        }

        return $keywords;
    }

    /**
     * 泰文分词
     * @param string $text
     * @return array
     */
    protected function thaiSegmentation(string $text): array
    {
        $keywords = [];

        // 泰文没有空格，使用字符组合
        preg_match_all('/[\x{0e00}-\x{0e7f}]{2,6}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }

        return $keywords;
    }

    /**
     * 通用分词（处理混合语言和未知语言）
     * @param string $text
     * @return array
     */
    protected function universalSegmentation(string $text): array
    {
        $keywords = [];

        // 1. 按空格分割
        $words = preg_split('/\s+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        foreach ($words as $word) {
            if (mb_strlen($word) >= 2) {
                $keywords[] = $word;
            }
        }

        // 2. 提取数字
        preg_match_all('/\d{2,}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }

        // 3. 提取英文单词
        preg_match_all('/[a-zA-Z]{2,}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }

        return $keywords;
    }

    /**
     * 多语言过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @param array $detectedLanguages
     * @return array
     */
    protected function multiLanguageFilterAndRank(array $keywords, string $originalText, array $detectedLanguages): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);

        // 2. 多语言过滤
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidMultiLanguageKeyword($keyword, $detectedLanguages)) {
                $filtered[] = $keyword;
            }
        }

        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateMultiLanguageWeight($keyword, $originalText, $detectedLanguages);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }

        // 按权重排序
        arsort($weighted);

        return array_keys($weighted);
    }

    /**
     * 验证多语言关键词有效性
     * @param string $keyword
     * @param array $detectedLanguages
     * @return bool
     */
    protected function isValidMultiLanguageKeyword(string $keyword, array $detectedLanguages): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 15) {
            return false;
        }

        // 检查是否为停用词
        foreach ($detectedLanguages as $lang => $confidence) {
            if (isset($this->multiLanguageStopWords[$lang]) &&
                in_array(mb_strtolower($keyword), $this->multiLanguageStopWords[$lang])) {
                return false;
            }
        }

        // 纯空格检查
        if (trim($keyword) === '') {
            return false;
        }

        return true;
    }

    /**
     * 计算多语言权重
     * @param string $keyword
     * @param string $text
     * @param array $detectedLanguages
     * @return float
     */
    protected function calculateMultiLanguageWeight(string $keyword, string $text, array $detectedLanguages): float
    {
        $weight = 0.0;

        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += ($length - 1) * 0.1;

        // 2. 频率权重
        $frequency = substr_count(mb_strtolower($text), mb_strtolower($keyword));
        $weight += $frequency * 0.3;

        // 3. 业务词典权重
        foreach ($detectedLanguages as $lang => $confidence) {
            if (isset($this->multiLanguageBusinessDict[$lang][$keyword])) {
                $weight += $this->multiLanguageBusinessDict[$lang][$keyword] * 0.1 * $confidence;
            }
        }

        // 4. 语言匹配权重
        foreach ($detectedLanguages as $lang => $confidence) {
            if ($this->matchesLanguagePattern($keyword, $lang)) {
                $weight += $confidence * 0.5;
            }
        }

        return $weight;
    }

    /**
     * 检查关键词是否匹配语言模式
     * @param string $keyword
     * @param string $language
     * @return bool
     */
    protected function matchesLanguagePattern(string $keyword, string $language): bool
    {
        if (!isset($this->languagePatterns[$language])) {
            return false;
        }

        return preg_match($this->languagePatterns[$language], $keyword);
    }

    /**
     * 获取支持的语言列表
     * @return array
     */
    public function getSupportedLanguages(): array
    {
        return $this->supportedLanguages;
    }

    /**
     * 调试信息：显示多语言分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $detectedLanguages = $this->detectLanguages($text);
        $primaryLanguage = array_key_first($detectedLanguages);

        $debug = [
            'original' => $text,
            'detected_languages' => $detectedLanguages,
            'primary_language' => $primaryLanguage,
            'specialized_service' => null,
            'specialized_result' => [],
            'universal_segmentation' => $this->universalSegmentation($text),
            'final_keywords' => $this->extractKeywords($text)
        ];

        // 如果有专门的分词服务，显示其结果
        $service = $this->getSegmentationService($primaryLanguage);
        if ($service) {
            $debug['specialized_service'] = get_class($service);
            if (method_exists($service, 'debugSegmentation')) {
                $debug['specialized_result'] = $service->debugSegmentation($text);
            } else {
                $debug['specialized_result'] = $service->extractKeywords($text);
            }
        }

        return $debug;
    }

    /**
     * 获取所有可用的分词服务信息
     * @return array
     */
    public function getAvailableServices(): array
    {
        $services = [];

        foreach ($this->serviceMapping as $language => $serviceClass) {
            $services[$language] = [
                'language_name' => $this->supportedLanguages[$language] ?? $language,
                'service_class' => $serviceClass,
                'is_specialized' => $serviceClass !== 'universal',
                'is_loaded' => isset($this->segmentationServices[$language])
            ];
        }

        return $services;
    }

    /**
     * 预加载指定语言的分词服务
     * @param array $languages
     * @return array 成功加载的语言列表
     */
    public function preloadServices(array $languages): array
    {
        $loaded = [];

        foreach ($languages as $language) {
            $service = $this->getSegmentationService($language);
            if ($service) {
                $loaded[] = $language;
            }
        }

        return $loaded;
    }

    /**
     * 清理未使用的分词服务实例
     */
    public function cleanupServices(): void
    {
        $this->segmentationServices = [];
    }
}