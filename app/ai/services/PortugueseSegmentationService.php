<?php

namespace app\ai\services;

/**
 * 葡萄牙文分词服务
 * 参照 ChineseSegmentationService 架构设计
 */
class PortugueseSegmentationService
{
    /**
     * 葡萄牙文停用词列表
     * @var array
     */
    protected array $stopWords = [
        'o', 'a', 'os', 'as', 'um', 'uma', 'uns', 'umas', 'e', 'ou', 'mas', 'porém', 'contudo', 'todavia',
        'em', 'de', 'para', 'por', 'com', 'sem', 'sobre', 'sob', 'entre', 'desde', 'até', 'através',
        'eu', 'tu', 'ele', 'ela', 'nós', 'vós', 'eles', 'elas', 'me', 'te', 'se', 'nos', 'vos', 'lhe', 'lhes',
        'que', 'quando', 'onde', 'por que', 'como', 'qual', 'quem', 'quanto', 'quantos', 'quantas',
        'por favor', 'obrigado', 'obrigada', 'olá', 'desculpe', 'tchau', 'bom dia', 'boa tarde', 'boa noite',
        'este', 'esta', 'isto', 'estes', 'estas', 'esse', 'essa', 'isso', 'esses', 'essas', 'aquele', 'aquela',
        'ser', 'estar', 'ter', 'haver', 'fazer', 'poder', 'dever', 'querer', 'saber', 'ir', 'vir', 'dar', 'ver'
    ];

    /**
     * 葡萄牙文核心业务词典
     * @var array
     */
    protected array $coreBusinessDict = [
        // 电商平台
        'amazon' => 10, 'mercado livre' => 10, 'americanas' => 9, 'submarino' => 8, 'magazine luiza' => 8,
        'casas bahia' => 8, 'extra' => 7, 'ponto frio' => 7, 'shoptime' => 7, 'netshoes' => 7,
        
        // 支付方式
        'paypal' => 10, 'pix' => 10, 'visa' => 9, 'mastercard' => 9, 'american express' => 8,
        'cartão de crédito' => 9, 'cartão de débito' => 7, 'transferência bancária' => 8, 'dinheiro' => 6,
        'boleto bancário' => 8, 'mercado pago' => 9, 'pagseguro' => 8,
        
        // 核心业务动作
        'comprar' => 10, 'compra' => 10, 'pagamento' => 10, 'pagar' => 10, 'recarga' => 9,
        'reembolso' => 10, 'devolver' => 9, 'troca' => 8, 'pedido' => 10, 'entrega' => 9,
        'recebimento' => 8, 'produto' => 9, 'item' => 8, 'preço' => 8, 'custo' => 7,
        'desconto' => 8, 'oferta' => 8, 'cupom' => 7, 'promoção' => 7, 'pontos' => 7,
        
        // 账户相关
        'login' => 9, 'cadastro' => 9, 'assinatura' => 8, 'logout' => 7, 'conta' => 9,
        'perfil' => 7, 'senha' => 9, 'email' => 8, 'usuário' => 8, 'verificação' => 8,
        
        // 客户服务
        'suporte' => 9, 'ajuda' => 8, 'atendimento ao cliente' => 10, 'dúvida' => 8, 'chat' => 7,
        'telefone' => 7, 'suporte por email' => 8, 'ticket' => 7, 'reclamação' => 8, 'feedback' => 7,
        
        // 产品类别
        'eletrônicos' => 8, 'roupas' => 7, 'livros' => 7, 'casa' => 6, 'esportes' => 7,
        'brinquedos' => 7, 'beleza' => 7, 'saúde' => 7, 'automóvel' => 7, 'comida' => 7
    ];

    /**
     * 葡萄牙文扩展业务词典
     * @var array
     */
    protected array $extendedBusinessDict = [
        // 购物动作
        'navegar' => 6, 'buscar' => 7, 'filtrar' => 6, 'ordenar' => 5, 'comparar' => 6,
        'lista de desejos' => 6, 'carrinho' => 7, 'cesta' => 6, 'favoritos' => 6, 'salvar' => 5,
        
        // 订单状态
        'pendente' => 6, 'processando' => 7, 'enviado' => 7, 'entregue' => 7, 'cancelado' => 7,
        'confirmado' => 6, 'concluído' => 6, 'falhou' => 6, 'expirado' => 5,
        
        // 产品属性
        'marca' => 6, 'modelo' => 6, 'tamanho' => 6, 'cor' => 6, 'peso' => 5,
        'material' => 5, 'qualidade' => 6, 'condição' => 6, 'novo' => 5, 'usado' => 5,
        
        // 时间相关
        'hoje' => 5, 'amanhã' => 5, 'ontem' => 5, 'semana' => 5, 'mês' => 5,
        'dias úteis' => 6, 'fim de semana' => 5, 'feriado' => 5, 'horas' => 5,
        
        // 数量和测量
        'quantidade' => 6, 'valor' => 6, 'total' => 6, 'subtotal' => 6, 'imposto' => 6,
        'taxa' => 6, 'custo' => 6, 'tarifa' => 5, 'porcentagem' => 5
    ];

    /**
     * 葡萄牙文组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        'atendimento ao cliente', 'cartão de crédito', 'cartão presente', 'carrinho de compras', 'lista de desejos',
        'conta do usuário', 'endereço de email', 'número de telefone', 'código postal', 'endereço',
        'dias úteis', 'horário de trabalho', 'política de devolução', 'política de privacidade', 'termos de serviço',
        'entrega grátis', 'entrega rápida', 'entrega no mesmo dia', 'entrega no próximo dia',
        'compras online', 'aplicativo móvel', 'navegador web', 'mecanismo de busca', 'redes sociais',
        'avaliação do produto', 'avaliação do cliente', 'classificação por estrelas', 'descrição do produto',
        'histórico de pedidos', 'histórico de compras', 'método de pagamento', 'endereço de cobrança', 'endereço de entrega'
    ];

    /**
     * 提取葡萄牙文关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 多种分词策略
        $keywords = [];
        
        // 策略1: 组合词优先
        $compoundKeywords = $this->extractCompoundWords($cleanText);
        $keywords = array_merge($keywords, $compoundKeywords);
        
        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);
        
        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);
        
        // 策略4: 葡萄牙文词汇分词
        $wordKeywords = $this->portugueseWordSegmentation($cleanText);
        $keywords = array_merge($keywords, $wordKeywords);
        
        // 策略5: 词干提取
        $stemKeywords = $this->portugueseStemming($cleanText);
        $keywords = array_merge($keywords, $stemKeywords);
        
        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);
        
        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理葡萄牙文文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 转换为小写
        $text = mb_strtolower($text);
        
        // 处理葡萄牙文特殊字符
        $text = $this->normalizePortugueseText($text);
        
        // 移除特殊符号，保留字母、数字、空格
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $text);
        
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 规范化葡萄牙文文本
     * @param string $text
     * @return string
     */
    protected function normalizePortugueseText(string $text): string
    {
        // 保留重音符号，但统一某些变体
        $normalizations = [
            'á' => 'á', 'à' => 'à', 'â' => 'â', 'ã' => 'ã', // 保持重音
            'é' => 'é', 'ê' => 'ê', 'è' => 'è',
            'í' => 'í', 'î' => 'î', 'ì' => 'ì',
            'ó' => 'ó', 'ô' => 'ô', 'õ' => 'õ', 'ò' => 'ò',
            'ú' => 'ú', 'û' => 'û', 'ù' => 'ù',
            'ç' => 'ç', // 保持cedilla
        ];
        
        foreach ($normalizations as $from => $to) {
            $text = str_replace($from, $to, $text);
        }
        
        return $text;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->compoundWords as $compound) {
            if (mb_stripos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->coreBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 葡萄牙文词汇分词
     * @param string $text
     * @return array
     */
    protected function portugueseWordSegmentation(string $text): array
    {
        $keywords = [];
        
        // 按空格和标点分割
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 3 && preg_match('/^[a-záàâãéêíóôõúç]+$/iu', $word)) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 葡萄牙文词干提取
     * @param string $text
     * @return array
     */
    protected function portugueseStemming(string $text): array
    {
        $keywords = [];
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 4) {
                $stem = $this->portugueseStem($word);
                if ($stem !== $word && mb_strlen($stem) >= 3) {
                    $keywords[] = $stem;
                }
            }
        }
        
        return $keywords;
    }

    /**
     * 葡萄牙文词干提取算法
     * @param string $word
     * @return string
     */
    protected function portugueseStem(string $word): string
    {
        $word = mb_strtolower($word);
        
        // 去除常见后缀
        $suffixes = [
            // 动词后缀
            'ando', 'endo', 'indo', 'ado', 'ido', 'ar', 'er', 'ir',
            // 名词后缀
            'ção', 'são', 'dade', 'idade', 'ância', 'ência', 'mento', 'amento',
            // 形容词后缀
            'oso', 'osa', 'ivo', 'iva', 'ável', 'ível',
            // 复数形式
            'os', 'as', 'es', 'ões',
            // 其他常见后缀
            'mente', 'inho', 'inha', 'ão', 'ões'
        ];
        
        foreach ($suffixes as $suffix) {
            if (mb_strlen($word) > mb_strlen($suffix) + 2 && 
                mb_substr($word, -mb_strlen($suffix)) === $suffix) {
                return mb_substr($word, 0, -mb_strlen($suffix));
            }
        }
        
        return $word;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无效词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidPortugueseKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculatePortugueseWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 验证葡萄牙文关键词有效性
     * @param string $keyword
     * @return bool
     */
    protected function isValidPortugueseKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 20) {
            return false;
        }
        
        // 停用词检查
        if (in_array(mb_strtolower($keyword), $this->stopWords)) {
            return false;
        }
        
        // 必须包含字母
        if (!preg_match('/[a-záàâãéêíóôõúç]/iu', $keyword)) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算葡萄牙文关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculatePortugueseWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += $length * 0.1;
        
        // 2. 频率权重
        $frequency = substr_count(mb_strtolower($text), mb_strtolower($keyword));
        $weight += $frequency * 0.3;
        
        // 3. 业务词典权重
        if (isset($this->coreBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->coreBusinessDict[mb_strtolower($keyword)] * 0.1;
        } elseif (isset($this->extendedBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->extendedBusinessDict[mb_strtolower($keyword)] * 0.05;
        }
        
        // 4. 组合词权重
        if (in_array(mb_strtolower($keyword), $this->compoundWords)) {
            $weight += 2.0;
        }
        
        // 5. 重音符号权重（葡萄牙文特色）
        if (preg_match('/[áàâãéêíóôõúç]/iu', $keyword)) {
            $weight += 0.2;
        }
        
        // 6. 巴西特色词汇权重
        $brazilianTerms = ['mercado livre', 'pix', 'boleto', 'pagseguro', 'americanas'];
        foreach ($brazilianTerms as $term) {
            if (mb_stripos($keyword, $term) !== false) {
                $weight += 0.3;
                break;
            }
        }
        
        return $weight;
    }

    /**
     * 调试信息：显示葡萄牙文分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'normalized' => $this->normalizePortugueseText($text),
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'word_segmentation' => $this->portugueseWordSegmentation($cleanText),
            'stemming' => $this->portugueseStemming($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
