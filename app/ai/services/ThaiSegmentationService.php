<?php

namespace app\ai\services;

/**
 * 泰语分词服务
 * 参照 ChineseSegmentationService 架构设计
 */
class ThaiSegmentationService
{
    /**
     * 泰语停用词列表
     * @var array
     */
    protected array $stopWords = [
        'ใน', 'บน', 'ที่', 'จาก', 'ไป', 'มา', 'กับ', 'โดย', 'เพื่อ', 'ของ', 'แล้ว', 'แต่', 'หรือ', 'และ',
        'ฉัน', 'คุณ', 'เขา', 'เธอ', 'เรา', 'พวกเขา', 'มัน', 'นี้', 'นั้น', 'เหล่านี้', 'เหล่านั้น',
        'อะไร', 'เมื่อไหร่', 'ที่ไหน', 'ทำไม', 'อย่างไร', 'ใคร', 'กี่', 'เท่าไหร่',
        'โปรด', 'ขอบคุณ', 'สวัสดี', 'ขอโทษ', 'ลาก่อน', 'สบายดี', 'ไม่เป็นไร',
        'นี่', 'นั่น', 'ที่นี่', 'ที่นั่น', 'ตรงนี้', 'ตรงนั้น',
        'เป็น', 'คือ', 'มี', 'ไม่', 'ไม่มี', 'จะ', 'ได้', 'ต้อง', 'ควร', 'อาจ', 'คง'
    ];

    /**
     * 泰语核心业务词典
     * @var array
     */
    protected array $coreBusinessDict = [
        // 电商平台
        'ลาซาด้า' => 10, 'ช้อปปี้' => 10, 'เจดี' => 9, 'โลตัส' => 8, 'บิ๊กซี' => 8,
        'เซ็นทรัล' => 8, 'โรบินสัน' => 7, 'เทสโก้' => 7, 'แม็คโคร' => 7,
        
        // 支付方式
        'ทรูมันนี่' => 9, 'พร้อมเพย์' => 9, 'ไลน์เพย์' => 8, 'วีซ่า' => 8, 'มาสเตอร์การ์ด' => 8,
        'บัตรเครดิต' => 9, 'บัตรเดบิต' => 7, 'โอนเงิน' => 8, 'เงินสด' => 6,
        
        // 核心业务动作
        'ซื้อ' => 10, 'การซื้อ' => 10, 'จ่าย' => 10, 'การจ่าย' => 10, 'เติมเงิน' => 9,
        'คืนเงิน' => 10, 'คืนสินค้า' => 9, 'แลกเปลี่ยน' => 8, 'สั่งซื้อ' => 10, 'จัดส่ง' => 9,
        'ส่งมอบ' => 8, 'สินค้า' => 9, 'ผลิตภัณฑ์' => 8, 'ราคา' => 8, 'ค่าใช้จ่าย' => 7,
        'ส่วนลด' => 8, 'โปรโมชั่น' => 8, 'คูปอง' => 7, 'แคมเปญ' => 7, 'แต้ม' => 7,
        
        // 账户相关
        'เข้าสู่ระบบ' => 9, 'สมัครสมาชิก' => 9, 'สมาชิก' => 8, 'ออกจากระบบ' => 7, 'บัญชี' => 9,
        'โปรไฟล์' => 7, 'รหัสผ่าน' => 9, 'อีเมล' => 8, 'ชื่อผู้ใช้' => 8, 'ยืนยัน' => 8,
        
        // 客户服务
        'สนับสนุน' => 9, 'ช่วยเหลือ' => 8, 'บริการลูกค้า' => 10, 'สอบถาม' => 8, 'แชท' => 7,
        'โทรศัพท์' => 7, 'อีเมลสนับสนุน' => 8, 'ตั๋ว' => 7, 'ร้องเรียน' => 8, 'ความคิดเห็น' => 7,
        
        // 产品类别
        'อิเล็กทรอนิกส์' => 8, 'เสื้อผ้า' => 7, 'หนังสือ' => 7, 'บ้าน' => 6, 'กีฬา' => 7,
        'ของเล่น' => 7, 'ความงาม' => 7, 'สุขภาพ' => 7, 'รถยนต์' => 7, 'อาหาร' => 7
    ];

    /**
     * 泰语扩展业务词典
     * @var array
     */
    protected array $extendedBusinessDict = [
        // 购物动作
        'เรียกดู' => 6, 'ค้นหา' => 7, 'กรอง' => 6, 'เรียงลำดับ' => 5, 'เปรียบเทียบ' => 6,
        'รายการโปรด' => 6, 'ตะกร้า' => 7, 'รายการสินค้า' => 6, 'บันทึก' => 5,
        
        // 订单状态
        'รอดำเนินการ' => 6, 'กำลังดำเนินการ' => 7, 'จัดส่งแล้ว' => 7, 'ส่งมอบแล้ว' => 7, 'ยกเลิก' => 7,
        'ยืนยันแล้ว' => 6, 'เสร็จสิ้น' => 6, 'ล้มเหลว' => 6, 'หมดอายุ' => 5,
        
        // 产品属性
        'แบรนด์' => 6, 'รุ่น' => 6, 'ขนาด' => 6, 'สี' => 6, 'น้ำหนัก' => 5,
        'วัสดุ' => 5, 'คุณภาพ' => 6, 'สภาพ' => 6, 'ใหม่' => 5, 'มือสอง' => 5,
        
        // 时间相关
        'วันนี้' => 5, 'พรุ่งนี้' => 5, 'เมื่อวาน' => 5, 'สัปดาห์' => 5, 'เดือน' => 5,
        'วันทำการ' => 6, 'วันหยุด' => 5, 'วันหยุดนักขัตฤกษ์' => 5, 'ชั่วโมง' => 5,
        
        // 数量和测量
        'จำนวน' => 6, 'จำนวนเงิน' => 6, 'รวม' => 6, 'รวมย่อย' => 6, 'ภาษี' => 6,
        'ค่าธรรมเนียม' => 6, 'ค่าใช้จ่าย' => 6, 'อัตรา' => 5, 'เปอร์เซ็นต์' => 5
    ];

    /**
     * 泰语组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        'บริการลูกค้า', 'บัตรเครดิต', 'บัตรของขวัญ', 'ตะกร้าสินค้า', 'รายการโปรด',
        'บัญชีผู้ใช้', 'ที่อยู่อีเมล', 'หมายเลขโทรศัพท์', 'รหัสไปรษณีย์', 'ที่อยู่',
        'วันทำการ', 'เวลาทำการ', 'นโยบายการคืนสินค้า', 'นโยบายความเป็นส่วนตัว', 'เงื่อนไขการบริการ',
        'จัดส่งฟรี', 'จัดส่งเร็ว', 'จัดส่งในวันเดียวกัน', 'จัดส่งวันถัดไป',
        'ช้อปปิ้งออนไลน์', 'แอปมือถือ', 'เว็บเบราว์เซอร์', 'เครื่องมือค้นหา', 'สื่อสังคม',
        'รีวิวสินค้า', 'รีวิวลูกค้า', 'การให้คะแนนดาว', 'รายละเอียดสินค้า',
        'ประวัติการสั่งซื้อ', 'ประวัติการซื้อ', 'วิธีการชำระเงิน', 'ที่อยู่เรียกเก็บเงิน', 'ที่อยู่จัดส่ง'
    ];

    /**
     * 提取泰语关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 多种分词策略
        $keywords = [];
        
        // 策略1: 组合词优先
        $compoundKeywords = $this->extractCompoundWords($cleanText);
        $keywords = array_merge($keywords, $compoundKeywords);
        
        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);
        
        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);
        
        // 策略4: 泰语字符分词
        $characterKeywords = $this->thaiCharacterSegmentation($cleanText);
        $keywords = array_merge($keywords, $characterKeywords);
        
        // 策略5: 泰语音节分词
        $syllableKeywords = $this->thaiSyllableSegmentation($cleanText);
        $keywords = array_merge($keywords, $syllableKeywords);
        
        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);
        
        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理泰语文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 移除特殊符号，但保留泰语字符
        $text = preg_replace('/[^\p{Thai}\p{N}\s]/u', ' ', $text);
        
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->compoundWords as $compound) {
            if (strpos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->coreBusinessDict as $word => $weight) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 泰语字符分词
     * @param string $text
     * @return array
     */
    protected function thaiCharacterSegmentation(string $text): array
    {
        $keywords = [];
        
        // 提取泰语词汇（2-8字符）
        preg_match_all('/[\p{Thai}]{2,8}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        // 提取英文单词
        preg_match_all('/[a-zA-Z]{2,}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        // 提取数字
        preg_match_all('/\d{2,}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        return $keywords;
    }

    /**
     * 泰语音节分词
     * @param string $text
     * @return array
     */
    protected function thaiSyllableSegmentation(string $text): array
    {
        $keywords = [];
        $length = mb_strlen($text);
        
        // 泰语没有空格，使用滑动窗口提取可能的词汇
        for ($i = 0; $i < $length; $i++) {
            // 2-6字符的组合
            for ($len = 2; $len <= 6 && $i + $len <= $length; $len++) {
                $segment = mb_substr($text, $i, $len);
                if ($this->isValidThaiSegment($segment)) {
                    $keywords[] = $segment;
                }
            }
        }
        
        return $keywords;
    }

    /**
     * 判断泰语片段是否有效
     * @param string $segment
     * @return bool
     */
    protected function isValidThaiSegment(string $segment): bool
    {
        // 必须包含泰语字符
        if (!preg_match('/[\p{Thai}]/u', $segment)) {
            return false;
        }
        
        // 不能全是声调符号或特殊符号
        if (preg_match('/^[\u{0E48}-\u{0E4B}\u{0E4C}-\u{0E4F}]+$/u', $segment)) {
            return false;
        }
        
        // 检查是否有业务相关性
        return $this->hasThaiBusinessRelevance($segment);
    }

    /**
     * 判断是否有泰语业务相关性
     * @param string $text
     * @return bool
     */
    protected function hasThaiBusinessRelevance(string $text): bool
    {
        // 检查是否包含业务相关字符
        $businessChars = ['ซื้', 'จ่า', 'สั่', 'ส่ง', 'สิน', 'ค้า', 'ราค', 'ลูก', 'ค้า', 'บริ', 'การ'];
        
        foreach ($businessChars as $char) {
            if (strpos($text, $char) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无效词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidThaiKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateThaiWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 验证泰语关键词有效性
     * @param string $keyword
     * @return bool
     */
    protected function isValidThaiKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 15) {
            return false;
        }
        
        // 停用词检查
        if (in_array($keyword, $this->stopWords)) {
            return false;
        }
        
        // 纯空格检查
        if (trim($keyword) === '') {
            return false;
        }
        
        return true;
    }

    /**
     * 计算泰语关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateThaiWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += $length * 0.1;
        
        // 2. 频率权重
        $frequency = substr_count($text, $keyword);
        $weight += $frequency * 0.3;
        
        // 3. 业务词典权重
        if (isset($this->coreBusinessDict[$keyword])) {
            $weight += $this->coreBusinessDict[$keyword] * 0.1;
        } elseif (isset($this->extendedBusinessDict[$keyword])) {
            $weight += $this->extendedBusinessDict[$keyword] * 0.05;
        }
        
        // 4. 组合词权重
        if (in_array($keyword, $this->compoundWords)) {
            $weight += 2.0;
        }
        
        // 5. 泰语字符权重
        if (preg_match('/[\p{Thai}]/', $keyword)) {
            $weight += 0.3;
        }
        
        // 6. 业务相关性权重
        if ($this->hasThaiBusinessRelevance($keyword)) {
            $weight += 0.5;
        }
        
        return $weight;
    }

    /**
     * 调试信息：显示泰语分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'character_segmentation' => $this->thaiCharacterSegmentation($cleanText),
            'syllable_segmentation' => $this->thaiSyllableSegmentation($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
