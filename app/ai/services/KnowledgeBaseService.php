<?php

namespace app\ai\services;

use app\model\Help;
use app\model\Category;
use app\ai\services\UnifiedAiService;
use app\ai\memory\MySqlMemory;
use app\ai\config\ConfigManager;
use app\ai\utils\Logger;
use think\facade\Cache;
use think\facade\Db;

/**
 * AI知识库服务
 * 整合帮助文档与AI功能，提供智能问答
 */
class KnowledgeBaseService
{
    /**
     * AI服务
     * @var UnifiedAiService
     */
    protected $aiService;
    
    /**
     * 记忆服务
     * @var MySqlMemory
     */
    protected $memory;
    
    /**
     * 缓存前缀
     * @var string
     */
    protected $cachePrefix = 'ai_kb_';
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->aiService = new UnifiedAiService();
        $this->memory = new MySqlMemory();
    }
    
    /**
     * 智能问答
     * @param string $question 用户问题
     * @param array $options 选项
     * @return array
     */
    public function ask(string $question, array $options = []): array
    {
        try {
            $sessionId = $options['session_id'] ?? 'kb_' . uniqid();
            $useMemory = $options['use_memory'] ?? true;
            
            // 1. 搜索相关帮助文档
            $relevantHelps = $this->searchRelevantHelps($question);
            
            // 2. 构建上下文
            $context = $this->buildContext($question, $relevantHelps, $sessionId, $useMemory);
            
            // 3. 生成AI回复
            $response = $this->generateAiResponse($question, $context, $sessionId);
            
            // 4. 保存对话记录
            if ($useMemory) {
                $this->saveConversation($sessionId, $question, $response['content'], $relevantHelps);
            }
            
            return [
                'success' => true,
                'content' => $response['content'],
                'sources' => $relevantHelps,
                'session_id' => $sessionId,
                'confidence' => $this->calculateConfidence($relevantHelps),
                'suggestions' => $this->generateSuggestions($question, $relevantHelps)
            ];
            
        } catch (\Exception $e) {
            Logger::error('Knowledge base query failed', [
                'question' => $question,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'content' => '抱歉，我暂时无法回答您的问题，请稍后再试或联系人工客服。',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 搜索相关帮助文档
     * @param string $question
     * @return array
     */
    protected function searchRelevantHelps(string $question): array
    {
        // 缓存键
        $cacheKey = $this->cachePrefix . 'search_' . md5($question);
        $cached = Cache::get($cacheKey);
        
        if ($cached !== false) {
            return $cached;
        }
        
        // 1. 关键词匹配搜索
        $keywordResults = $this->keywordSearch($question);
        
        // 2. 语义相似度搜索（如果有向量数据库）
        $semanticResults = $this->semanticSearch($question);
        
        // 3. 合并和排序结果
        $results = $this->mergeAndRankResults($keywordResults, $semanticResults);
        
        // 缓存结果（5分钟）
        Cache::set($cacheKey, $results, 300);
        
        return $results;
    }
    
    /**
     * 关键词搜索
     * @param string $question
     * @return array
     */
    protected function keywordSearch(string $question): array
    {
        // 提取关键词
        $keywords = $this->extractKeywords($question);

        Logger::info('Keyword search', ['keywords' => $keywords]);
        
        if (empty($keywords)) {
            return [];
        }
        
        // 构建搜索条件
        $query = Help::where('enabled', 1);
        
        foreach ($keywords as $keyword) {
            $query->whereOr(function($q) use ($keyword) {
                $q->where('title', 'like', "%{$keyword}%")
                  ->whereOr('content', 'like', "%{$keyword}%");
            });
        }
        
        $helps = $query->with(['category'])
            ->field('id,title,content,category,sort')
            ->order('sort', 'desc')
            ->limit(10)
            ->select()
            ->toArray();
        Logger::info('Keyword search', ['helps' => $helps]);
        
        // 计算相关性分数
        foreach ($helps as &$help) {
            $help['relevance_score'] = $this->calculateKeywordRelevance($question, $help);
            $help['search_type'] = 'keyword';
        }
        
        return $helps;
    }
    
    /**
     * 语义搜索（简化版本）
     * @param string $question
     * @return array
     */
    protected function semanticSearch(string $question): array
    {
        // 这里可以集成向量数据库进行语义搜索
        // 目前使用简化的语义匹配
        
        $helps = Help::where('enabled', 1)
            ->field('id,title,content,category,sort')
            ->select()
            ->toArray();
        
        $results = [];
        foreach ($helps as $help) {
            $similarity = $this->calculateSemanticSimilarity($question, $help['title'] . ' ' . $help['content']);
            if ($similarity > 0.3) { // 阈值
                $help['relevance_score'] = $similarity;
                $help['search_type'] = 'semantic';
                $results[] = $help;
            }
        }
        
        // 按相关性排序
        usort($results, function($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });
        
        return array_slice($results, 0, 5);
    }
    
    /**
     * 合并和排序搜索结果
     * @param array $keywordResults
     * @param array $semanticResults
     * @return array
     */
    protected function mergeAndRankResults(array $keywordResults, array $semanticResults): array
    {
        $merged = [];
        $seen = [];
        
        // 合并结果，避免重复
        foreach (array_merge($keywordResults, $semanticResults) as $result) {
            if (!isset($seen[$result['id']])) {
                $merged[] = $result;
                $seen[$result['id']] = true;
            }
        }
        
        // 按相关性分数排序
        usort($merged, function($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });
        
        return array_slice($merged, 0, 5);
    }
    
    /**
     * 构建AI上下文
     * @param string $question
     * @param array $relevantHelps
     * @param string $sessionId
     * @param bool $useMemory
     * @return array
     */
    protected function buildContext(string $question, array $relevantHelps, string $sessionId, bool $useMemory): array
    {
        $context = [
            'question' => $question,
            'knowledge_base' => $this->formatKnowledgeBase($relevantHelps),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // 添加历史对话上下文
        if ($useMemory) {
            $history = $this->memory->getHistory($sessionId);
            $context['conversation_history'] = $this->formatConversationHistory($history);
        }
        
        return $context;
    }
    
    /**
     * 生成AI回复
     * @param string $question
     * @param array $context
     * @param string $sessionId
     * @return array
     */
    protected function generateAiResponse(string $question, array $context, string $sessionId): array
    {
        // 构建提示词
        $prompt = $this->buildPrompt($question, $context);
        
        // 调用AI服务
        $response = $this->aiService->process($prompt, [
            'session_id' => $sessionId,
            'type' => 'knowledge_base',
            'temperature' => 0.7,
            'max_tokens' => 800
        ]);
        
        return [
            'content' => $response->content ?? $response,
            'tokens' => $response->tokens ?? 0,
            'model' => $response->model ?? 'unknown'
        ];
    }
    
    /**
     * 构建AI提示词
     * @param string $question
     * @param array $context
     * @return string
     */
    protected function buildPrompt(string $question, array $context): string
    {
        $prompt = "你是一个专业的客服助手，基于提供的知识库内容回答用户问题。\n\n";
        
        // 添加知识库内容
        if (!empty($context['knowledge_base'])) {
            $prompt .= "相关知识库内容：\n";
            foreach ($context['knowledge_base'] as $index => $kb) {
                $prompt .= ($index + 1) . ". 标题：{$kb['title']}\n";
                $prompt .= "   内容：{$kb['content']}\n\n";
            }
        }
        
        // 添加对话历史
        if (!empty($context['conversation_history'])) {
            $prompt .= "对话历史：\n{$context['conversation_history']}\n\n";
        }
        
        $prompt .= "用户问题：{$question}\n\n";
        $prompt .= "请基于上述知识库内容回答用户问题。要求：\n";
        $prompt .= "1. 回答要准确、专业、友好\n";
        $prompt .= "2. 如果知识库中有相关内容，请优先使用\n";
        $prompt .= "3. 如果知识库中没有相关内容，请诚实说明并建议联系人工客服\n";
        $prompt .= "4. 回答要简洁明了，避免冗长\n";
        $prompt .= "5. 可以适当提供相关的建议或补充信息\n\n";
        $prompt .= "回答：";
        
        return $prompt;
    }
    
    /**
     * 提取关键词
     * @param string $text
     * @return array
     */
    protected function extractKeywords(string $text): array
    {
        // 简化的关键词提取
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $text);
        $words = preg_split('/\s+/u', trim($text));
        
        // 过滤停用词和短词
        $stopWords = ['的', '了', '在', '是', '我', '你', '他', '她', '它', '们', '这', '那', '有', '没', '不', '要', '会', '能', '可以', '怎么', '什么', '为什么', '如何'];
        $keywords = [];
        
        foreach ($words as $word) {
            if (mb_strlen($word) >= 2 && !in_array($word, $stopWords)) {
                $keywords[] = $word;
            }
        }
        
        return array_unique($keywords);
    }
    
    /**
     * 计算关键词相关性
     * @param string $question
     * @param array $help
     * @return float
     */
    protected function calculateKeywordRelevance(string $question, array $help): float
    {
        $questionKeywords = $this->extractKeywords($question);
        $helpText = $help['title'] . ' ' . $help['content'];
        $helpKeywords = $this->extractKeywords($helpText);
        
        if (empty($questionKeywords) || empty($helpKeywords)) {
            return 0.0;
        }
        
        $intersection = array_intersect($questionKeywords, $helpKeywords);
        $union = array_unique(array_merge($questionKeywords, $helpKeywords));
        
        return count($intersection) / count($union);
    }
    
    /**
     * 计算语义相似度（简化版本）
     * @param string $text1
     * @param string $text2
     * @return float
     */
    protected function calculateSemanticSimilarity(string $text1, string $text2): float
    {
        // 这里可以使用更复杂的语义相似度算法
        // 目前使用简化的余弦相似度
        
        $words1 = $this->extractKeywords($text1);
        $words2 = $this->extractKeywords($text2);
        
        if (empty($words1) || empty($words2)) {
            return 0.0;
        }
        
        $intersection = array_intersect($words1, $words2);
        $magnitude1 = sqrt(count($words1));
        $magnitude2 = sqrt(count($words2));
        
        if ($magnitude1 == 0 || $magnitude2 == 0) {
            return 0.0;
        }
        
        return count($intersection) / ($magnitude1 * $magnitude2);
    }
    
    /**
     * 格式化知识库内容
     * @param array $helps
     * @return array
     */
    protected function formatKnowledgeBase(array $helps): array
    {
        $formatted = [];
        foreach ($helps as $help) {
            $formatted[] = [
                'id' => $help['id'],
                'title' => $help['title'],
                'content' => mb_substr(strip_tags($help['content']), 0, 500) . '...',
                'relevance_score' => $help['relevance_score'] ?? 0
            ];
        }
        return $formatted;
    }
    
    /**
     * 格式化对话历史
     * @param array $history
     * @return string
     */
    protected function formatConversationHistory(array $history): string
    {
        if (empty($history)) {
            return '';
        }
        
        $formatted = [];
        foreach (array_slice($history, -3) as $entry) { // 只取最近3条
            if (isset($entry['input']) && isset($entry['output'])) {
                $formatted[] = "用户：{$entry['input']}";
                $formatted[] = "助手：{$entry['output']}";
            }
        }
        
        return implode("\n", $formatted);
    }
    
    /**
     * 保存对话记录
     * @param string $sessionId
     * @param string $question
     * @param string $answer
     * @param array $sources
     */
    protected function saveConversation(string $sessionId, string $question, string $answer, array $sources): void
    {
        try {
            $this->memory->addMessage($sessionId, $question, 'input');
            $this->memory->addMessage($sessionId, $answer, 'output', [
                'sources' => array_column($sources, 'id'),
                'type' => 'knowledge_base'
            ]);
        } catch (\Exception $e) {
            Logger::error('Failed to save conversation', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 计算回答置信度
     * @param array $relevantHelps
     * @return float
     */
    protected function calculateConfidence(array $relevantHelps): float
    {
        if (empty($relevantHelps)) {
            return 0.1;
        }
        
        $maxScore = max(array_column($relevantHelps, 'relevance_score'));
        $avgScore = array_sum(array_column($relevantHelps, 'relevance_score')) / count($relevantHelps);
        
        return min(0.9, ($maxScore + $avgScore) / 2);
    }
    
    /**
     * 生成相关建议
     * @param string $question
     * @param array $relevantHelps
     * @return array
     */
    protected function generateSuggestions(string $question, array $relevantHelps): array
    {
        $suggestions = [];
        
        // 基于相关帮助文档生成建议
        foreach (array_slice($relevantHelps, 0, 3) as $help) {
            $suggestions[] = [
                'title' => $help['title'],
                'id' => $help['id']
            ];
        }
        
        return $suggestions;
    }
    
    /**
     * 获取热门问题
     * @param int $limit
     * @return array
     */
    public function getPopularQuestions(int $limit = 10): array
    {
        $cacheKey = $this->cachePrefix . 'popular_questions';
        $cached = Cache::get($cacheKey);
        
        if ($cached !== false) {
            return $cached;
        }
        
        // 从帮助文档中获取热门问题
        $popular = Help::where('enabled', 1)
            ->field('id,title,category')
            ->order('sort', 'desc')
            ->limit($limit)
            ->select()
            ->toArray();
        
        Cache::set($cacheKey, $popular, 1800); // 缓存30分钟
        
        return $popular;
    }
    
    /**
     * 获取分类下的问题
     * @param int $categoryId
     * @param int $limit
     * @return array
     */
    public function getQuestionsByCategory(int $categoryId, int $limit = 20): array
    {
        return Help::where('enabled', 1)
            ->where('category', $categoryId)
            ->field('id,title,content')
            ->order('sort', 'desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }
    
    /**
     * 获取问题详情
     * @param int $helpId
     * @return array|null
     */
    public function getQuestionDetail(int $helpId): ?array
    {
        $help = Help::where('id', $helpId)
            ->where('enabled', 1)
            ->find();
            
        if (!$help) {
            return null;
        }
        
        return $help->toArray();
    }
}
