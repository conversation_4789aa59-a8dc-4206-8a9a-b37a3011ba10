<?php

namespace app\ai\services;

/**
 * 日文分词服务
 * 参照 ChineseSegmentationService 架构设计
 */
class JapaneseSegmentationService
{
    /**
     * 日文停用词列表
     * @var array
     */
    protected array $stopWords = [
        'の', 'に', 'は', 'を', 'が', 'で', 'と', 'から', 'まで', 'より', 'へ', 'や', 'か', 'も', 
        'だ', 'である', 'です', 'ます', 'した', 'して', 'する', 'される', 'れる', 'られる',
        'これ', 'それ', 'あれ', 'どれ', 'ここ', 'そこ', 'あそこ', 'どこ', 'この', 'その', 'あの', 'どの',
        '私', 'あなた', '彼', '彼女', 'われわれ', '彼ら', '彼女ら', '自分', '相手',
        'なに', 'なん', 'いつ', 'どう', 'なぜ', 'どのように', 'どうして', 'いかが', 'どちら',
        'お願い', 'ありがとう', 'こんにちは', 'すみません', 'ごめん', 'はい', 'いいえ', 'そうです'
    ];

    /**
     * 日文核心业务词典
     * @var array
     */
    protected array $coreBusinessDict = [
        // 电商平台
        '楽天' => 10, 'アマゾン' => 10, 'ヤフー' => 9, 'メルカリ' => 9, 'ゾゾタウン' => 8,
        'ヨドバシ' => 8, 'ビックカメラ' => 8, 'ユニクロ' => 7, 'ニトリ' => 7,
        
        // 支付方式
        'ペイペイ' => 10, 'ライン' => 8, 'ペイ' => 8, 'クレジットカード' => 9, 'デビットカード' => 7,
        'コンビニ' => 7, '代引き' => 7, '銀行振込' => 7, 'ポイント' => 8,
        
        // 核心业务动作
        '購入' => 10, '買う' => 10, '支払い' => 10, '決済' => 9, 'チャージ' => 9,
        '返金' => 10, '返品' => 9, '交換' => 8, '注文' => 10, '配送' => 9,
        '配達' => 8, '商品' => 9, '製品' => 8, '価格' => 8, '値段' => 7,
        '割引' => 8, 'セール' => 8, 'クーポン' => 7, 'キャンペーン' => 7,
        
        // 账户相关
        'ログイン' => 9, '登録' => 9, 'サインアップ' => 8, 'サインイン' => 8, 'アカウント' => 9,
        'プロフィール' => 7, 'パスワード' => 9, 'メール' => 8, 'ユーザー名' => 7, '認証' => 8,
        
        // 客户服务
        'サポート' => 9, 'ヘルプ' => 8, 'カスタマーサービス' => 10, '問い合わせ' => 8, 'チャット' => 7,
        '電話' => 7, 'メールサポート' => 8, 'チケット' => 7, '苦情' => 8, 'フィードバック' => 7,
        
        // 产品类别
        '電子機器' => 8, '服' => 7, '本' => 7, '家' => 6, 'スポーツ' => 7,
        'おもちゃ' => 7, '美容' => 7, '健康' => 7, '自動車' => 7
    ];

    /**
     * 日文扩展业务词典
     * @var array
     */
    protected array $extendedBusinessDict = [
        // 购物动作
        '閲覧' => 6, '検索' => 7, 'フィルター' => 6, 'ソート' => 5, '比較' => 6,
        'ウィッシュリスト' => 6, 'カート' => 7, 'バスケット' => 6, 'お気に入り' => 6, '保存' => 5,
        
        // 订单状态
        '保留中' => 6, '処理中' => 7, '発送済み' => 7, '配達済み' => 7, 'キャンセル' => 7,
        '確認済み' => 6, '完了' => 6, '失敗' => 6, '期限切れ' => 5,
        
        // 产品属性
        'ブランド' => 6, 'モデル' => 6, 'サイズ' => 6, '色' => 6, '重量' => 5,
        '材料' => 5, '品質' => 6, '状態' => 6, '新品' => 5, '中古' => 5,
        
        // 时间相关
        '今日' => 5, '明日' => 5, '昨日' => 5, '週' => 5, '月' => 5,
        '営業日' => 6, '週末' => 5, '祝日' => 5, '時間' => 5,
        
        // 数量和测量
        '数量' => 6, '金額' => 6, '合計' => 6, '小計' => 6, '税' => 6,
        '手数料' => 6, '料金' => 6, '率' => 5, 'パーセント' => 5
    ];

    /**
     * 日文组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        'カスタマーサービス', 'クレジットカード', 'ギフトカード', 'ショッピングカート', 'ウィッシュリスト',
        'ユーザーアカウント', 'メールアドレス', '電話番号', '郵便番号', '住所',
        '営業日', '営業時間', '返品ポリシー', 'プライバシーポリシー', '利用規約',
        '送料無料', '速達配送', '当日配送', '翌日配送', 'オンラインショッピング',
        'モバイルアプリ', 'ウェブブラウザ', '検索エンジン', 'ソーシャルメディア',
        '商品レビュー', 'カスタマーレビュー', '星評価', '商品説明',
        '注文履歴', '購入履歴', '支払い方法', '請求先住所', '配送先住所'
    ];

    /**
     * 提取日文关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 多种分词策略
        $keywords = [];
        
        // 策略1: 组合词优先
        $compoundKeywords = $this->extractCompoundWords($cleanText);
        $keywords = array_merge($keywords, $compoundKeywords);
        
        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);
        
        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);
        
        // 策略4: 日文字符分词
        $characterKeywords = $this->japaneseCharacterSegmentation($cleanText);
        $keywords = array_merge($keywords, $characterKeywords);
        
        // 策略5: N-gram分词
        $ngramKeywords = $this->japaneseNgramSegmentation($cleanText);
        $keywords = array_merge($keywords, $ngramKeywords);
        
        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);
        
        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理日文文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        // 移除特殊符号，但保留日文字符
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $text);
        
        // 再次规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->compoundWords as $compound) {
            if (strpos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->coreBusinessDict as $word => $weight) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 日文字符分词
     * @param string $text
     * @return array
     */
    protected function japaneseCharacterSegmentation(string $text): array
    {
        $keywords = [];
        
        // 1. 提取平假名词汇（2-4字符）
        preg_match_all('/[\x{3040}-\x{309f}]{2,4}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        // 2. 提取片假名词汇（2-6字符）
        preg_match_all('/[\x{30a0}-\x{30ff}]{2,6}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        // 3. 提取汉字词汇（1-4字符）
        preg_match_all('/[\x{4e00}-\x{9fff}]{1,4}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        // 4. 提取英文单词
        preg_match_all('/[a-zA-Z]{2,}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        return $keywords;
    }

    /**
     * 日文N-gram分词
     * @param string $text
     * @return array
     */
    protected function japaneseNgramSegmentation(string $text): array
    {
        $keywords = [];
        $length = mb_strlen($text);
        
        // 2-gram
        for ($i = 0; $i < $length - 1; $i++) {
            $gram = mb_substr($text, $i, 2);
            if ($this->isValidJapaneseNgram($gram)) {
                $keywords[] = $gram;
            }
        }
        
        // 3-gram（选择性）
        for ($i = 0; $i < $length - 2; $i++) {
            $gram = mb_substr($text, $i, 3);
            if ($this->isValidJapaneseNgram($gram) && $this->hasJapaneseBusinessRelevance($gram)) {
                $keywords[] = $gram;
            }
        }
        
        return $keywords;
    }

    /**
     * 判断日文N-gram是否有效
     * @param string $gram
     * @return bool
     */
    protected function isValidJapaneseNgram(string $gram): bool
    {
        // 必须包含日文字符
        if (!preg_match('/[\x{3040}-\x{309f}\x{30a0}-\x{30ff}\x{4e00}-\x{9fff}]/u', $gram)) {
            return false;
        }
        
        // 不能全是停用词
        if (in_array($gram, $this->stopWords)) {
            return false;
        }
        
        return true;
    }

    /**
     * 判断是否有日文业务相关性
     * @param string $text
     * @return bool
     */
    protected function hasJapaneseBusinessRelevance(string $text): bool
    {
        // 检查是否包含业务相关字符
        $businessChars = ['購', '買', '支', '払', '決', '済', '注', '文', '配', '送', '商', '品', 
                         '登', '録', 'ロ', 'グ', 'イ', 'ン', 'サ', 'ポ', 'ー', 'ト', 'ヘ', 'ル', 'プ'];
        
        foreach ($businessChars as $char) {
            if (strpos($text, $char) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无效词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidJapaneseKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateJapaneseWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 验证日文关键词有效性
     * @param string $keyword
     * @return bool
     */
    protected function isValidJapaneseKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 1 || $length > 10) {
            return false;
        }
        
        // 停用词检查
        if (in_array($keyword, $this->stopWords)) {
            return false;
        }
        
        // 纯空格检查
        if (trim($keyword) === '') {
            return false;
        }
        
        return true;
    }

    /**
     * 计算日文关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateJapaneseWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += $length * 0.1;
        
        // 2. 频率权重
        $frequency = substr_count($text, $keyword);
        $weight += $frequency * 0.3;
        
        // 3. 业务词典权重
        if (isset($this->coreBusinessDict[$keyword])) {
            $weight += $this->coreBusinessDict[$keyword] * 0.1;
        } elseif (isset($this->extendedBusinessDict[$keyword])) {
            $weight += $this->extendedBusinessDict[$keyword] * 0.05;
        }
        
        // 4. 组合词权重
        if (in_array($keyword, $this->compoundWords)) {
            $weight += 2.0;
        }
        
        // 5. 字符类型权重
        if (preg_match('/[\x{30a0}-\x{30ff}]/', $keyword)) {
            $weight += 0.3; // 片假名权重
        }
        if (preg_match('/[\x{4e00}-\x{9fff}]/', $keyword)) {
            $weight += 0.2; // 汉字权重
        }
        
        // 6. 业务相关性权重
        if ($this->hasJapaneseBusinessRelevance($keyword)) {
            $weight += 0.5;
        }
        
        return $weight;
    }

    /**
     * 调试信息：显示日文分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'character_segmentation' => $this->japaneseCharacterSegmentation($cleanText),
            'ngram_segmentation' => $this->japaneseNgramSegmentation($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
