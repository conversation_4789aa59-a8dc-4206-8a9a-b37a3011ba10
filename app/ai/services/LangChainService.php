<?php

namespace app\ai\services;

use app\ai\exceptions\AiServiceException;
use app\ai\utils\Logger;
use app\ai\interfaces\MemoryInterface;
use app\ai\interfaces\PromptTemplateInterface;
use app\ai\memory\ConversationBufferMemory;
use app\ai\memory\MySqlMemory;
use app\ai\prompts\PromptTemplateManager;
use app\ai\prompts\BasePromptTemplate;
use app\ai\prompts\ChatPromptTemplate;
use app\ai\prompts\TemplateInitializer;
use app\ai\factories\MySqlMemoryFactory;

/**
 * LangChain服务
 * 提供统一的LangChain功能接口
 */
class LangChainService
{
    /**
     * 基础AI服务
     * @var BasicAiService
     */
    protected BasicAiService $basicAiService;
    
    /**
     * 记忆管理器
     * @var MemoryInterface
     */
    protected MemoryInterface $memory;
    
    /**
     * 工具集合
     * @var array
     */
    protected array $tools = [];
    
    /**
     * 构造函数
     * 
     * @param BasicAiService|null $basicAiService
     * @param MemoryInterface|null $memory
     */
    public function __construct(?BasicAiService $basicAiService = null, ?MemoryInterface $memory = null)
    {
        $this->basicAiService = $basicAiService ?? new BasicAiService();
        $this->memory = $memory ?? new ConversationBufferMemory($this->basicAiService);
        $this->initializeTools();
        
        Logger::info('LangChainService initialized');
    }
    
    /**
     * 初始化工具
     */
    protected function initializeTools(): void
    {
        $this->tools = [
            'calculator' => [
                'name' => 'calculator',
                'description' => '执行数学计算',
                'function' => [$this, 'calculateTool']
            ],
            'web_search' => [
                'name' => 'web_search',
                'description' => '搜索网络信息',
                'function' => [$this, 'webSearchTool']
            ],
            'text_analyzer' => [
                'name' => 'text_analyzer',
                'description' => '分析文本内容',
                'function' => [$this, 'textAnalyzerTool']
            ]
        ];
    }
    
    /**
     * 创建LLM链
     * 
     * @param string|PromptTemplateInterface $prompt 提示模板
     * @param array $variables 变量
     * @param array $options 选项参数
     * @param string|null $provider 指定服务提供商
     * @return array
     * @throws AiServiceException
     */
    public function createLLMChain($prompt, array $variables = [], array $options = [], ?string $provider = null): array
    {
        try {
            // 处理提示模板
            if ($prompt instanceof PromptTemplateInterface) {
                $processedPrompt = $prompt->format($variables);
            } elseif (is_string($prompt)) {
                // 兼容字符串模板
                $template = new BasePromptTemplate($prompt, array_keys($variables));
                $processedPrompt = $template->format($variables);
            } else {
                throw new AiServiceException('Prompt must be a string or PromptTemplateInterface instance');
            }
            
            $response = $this->basicAiService->chat($processedPrompt, $options, $provider);
            
            Logger::info('LLM Chain executed', [
                'provider' => $provider,
                'variables_count' => count($variables)
            ]);
            
            return [
                'output' => $response,
                'prompt' => $processedPrompt,
                'variables' => $variables
            ];
        } catch (\Exception $e) {
            Logger::error('LLM Chain execution failed', [
                'error' => $e->getMessage(),
                'provider' => $provider
            ]);
            throw new AiServiceException("LLM Chain execution failed: {$e->getMessage()}", 0, $e);
        }
    }
    
    /**
     * 创建顺序链
     * 
     * @param array $steps 处理步骤
     * @param array $initialInput 初始输入
     * @param array $options 选项参数
     * @param string|null $provider 指定服务提供商
     * @return array
     * @throws AiServiceException
     */
    public function createSequentialChain(array $steps, array $initialInput = [], array $options = [], ?string $provider = null): array
    {
        try {
            $results = [];
            $currentInput = $initialInput;
            
            foreach ($steps as $index => $step) {
                $stepPrompt = $step['prompt'] ?? '';
                $stepVariables = array_merge($currentInput, $step['variables'] ?? []);
                
                $stepResult = $this->createLLMChain($stepPrompt, $stepVariables, $options, $provider);
                
                $results["step_{$index}"] = $stepResult;
                
                // 将当前步骤的输出作为下一步骤的输入
                $currentInput['previous_output'] = $stepResult['output'];
                $currentInput = array_merge($currentInput, $step['output_variables'] ?? []);
            }
            
            Logger::info('Sequential Chain executed', [
                'steps_count' => count($steps),
                'provider' => $provider
            ]);
            
            return [
                'final_output' => end($results)['output'],
                'all_results' => $results,
                'steps_executed' => count($steps)
            ];
        } catch (\Exception $e) {
            Logger::error('Sequential Chain execution failed', [
                'error' => $e->getMessage(),
                'provider' => $provider
            ]);
            throw new AiServiceException("Sequential Chain execution failed: {$e->getMessage()}", 0, $e);
        }
    }
    
    /**
     * 创建ReAct代理
     * 
     * @param string $task 任务描述
     * @param array $availableTools 可用工具
     * @param array $options 选项参数
     * @param string|null $provider 指定服务提供商
     * @return array
     * @throws AiServiceException
     */
    public function createReActAgent(string $task, array $availableTools = [], array $options = [], ?string $provider = null): array
    {
        try {
            $maxIterations = $options['max_iterations'] ?? 5;
            $tools = empty($availableTools) ? array_keys($this->tools) : $availableTools;
            
            $thoughts = [];
            $actions = [];
            $observations = [];
            
            $currentThought = $task;
            
            for ($i = 0; $i < $maxIterations; $i++) {
                // 思考阶段
                $thinkPrompt = $this->buildReActThinkPrompt($currentThought, $tools, $thoughts, $actions, $observations);
                $thinkResponse = $this->basicAiService->chat($thinkPrompt, $options, $provider);
                
                $thoughts[] = $thinkResponse;
                
                // 解析是否需要执行动作
                $actionPlan = $this->parseActionFromThought($thinkResponse);
                
                if ($actionPlan['final_answer']) {
                    Logger::info('ReAct Agent completed', [
                        'iterations' => $i + 1,
                        'provider' => $provider
                    ]);
                    
                    return [
                        'final_answer' => $actionPlan['answer'],
                        'thoughts' => $thoughts,
                        'actions' => $actions,
                        'observations' => $observations,
                        'iterations' => $i + 1
                    ];
                }
                
                // 执行动作
                if ($actionPlan['action'] && isset($this->tools[$actionPlan['tool']])) {
                    $actionResult = $this->executeTool($actionPlan['tool'], $actionPlan['action_input']);
                    $actions[] = [
                        'tool' => $actionPlan['tool'],
                        'input' => $actionPlan['action_input'],
                        'result' => $actionResult
                    ];
                    $observations[] = $actionResult;
                    $currentThought = "Based on the observation: {$actionResult}, what should I do next?";
                } else {
                    $observations[] = "Tool not available or action not clear";
                    $currentThought = "I need to think of another approach.";
                }
            }
            
            Logger::info('ReAct Agent max iterations reached', [
                'max_iterations' => $maxIterations,
                'provider' => $provider
            ]);
            
            return [
                'final_answer' => 'Maximum iterations reached without final answer',
                'thoughts' => $thoughts,
                'actions' => $actions,
                'observations' => $observations,
                'iterations' => $maxIterations
            ];
        } catch (\Exception $e) {
            Logger::error('ReAct Agent execution failed', [
                'error' => $e->getMessage(),
                'provider' => $provider
            ]);
            throw new AiServiceException("ReAct Agent execution failed: {$e->getMessage()}", 0, $e);
        }
    }
    
    /**
     * 带记忆的对话
     * 
     * @param string $input 用户输入
     * @param string $sessionId 会话ID
     * @param array $options 选项参数
     * @param string|null $provider 指定服务提供商
     * @return string
     * @throws AiServiceException
     */
    public function executeWithMemory(string $input, string $sessionId, array $options = [], ?string $provider = null): string
    {
        try {
            // 获取会话记忆
            $conversationHistory = $this->memory->getHistory($sessionId);
            
            // 构建包含历史的消息
            $messages = [];
            foreach ($conversationHistory as $entry) {
                $messages[] = ['role' => 'user', 'content' => $entry['input']];
                $messages[] = ['role' => 'assistant', 'content' => $entry['output']];
            }
            $messages[] = ['role' => 'user', 'content' => $input];
            
            $response = $this->basicAiService->chat($messages, $options, $provider);
            
            // 保存到记忆
            $this->memory->saveMessage($sessionId, [
                'input' => $input,
                'output' => $response
            ]);
            
            Logger::info('Memory conversation executed', [
                'session_id' => $sessionId,
                'history_length' => count($conversationHistory),
                'provider' => $provider
            ]);
            
            return $response;
        } catch (\Exception $e) {
            Logger::error('Memory conversation failed', [
                'error' => $e->getMessage(),
                'session_id' => $sessionId,
                'provider' => $provider
            ]);
            throw new AiServiceException("Memory conversation failed: {$e->getMessage()}", 0, $e);
        }
    }
    
    /**
     * 执行工作流
     * 
     * @param array $workflow 工作流配置
     * @param array $initialData 初始数据
     * @param array $options 选项参数
     * @param string|null $provider 指定服务提供商
     * @return array
     * @throws AiServiceException
     */
    public function executeWorkflow(array $workflow, array $initialData = [], array $options = [], ?string $provider = null): array
    {
        try {
            $workflowType = $workflow['type'] ?? 'sequential';
            
            switch ($workflowType) {
                case 'sequential':
                    return $this->createSequentialChain($workflow['steps'] ?? [], $initialData, $options, $provider);
                case 'agent':
                    $task = $workflow['task'] ?? 'Complete the given task';
                    $tools = $workflow['tools'] ?? [];
                    return $this->createReActAgent($task, $tools, $options, $provider);
                case 'llm':
                    $prompt = $workflow['prompt'] ?? 'Process the following data: {data}';
                    $variables = array_merge($initialData, $workflow['variables'] ?? []);
                    return $this->createLLMChain($prompt, $variables, $options, $provider);
                default:
                    throw new AiServiceException("Unsupported workflow type: {$workflowType}");
            }
        } catch (\Exception $e) {
            Logger::error('Workflow execution failed', [
                'error' => $e->getMessage(),
                'workflow_type' => $workflow['type'] ?? 'unknown',
                'provider' => $provider
            ]);
            throw new AiServiceException("Workflow execution failed: {$e->getMessage()}", 0, $e);
        }
    }
    

    
    /**
     * 构建ReAct思考提示
     * 
     * @param string $task 任务
     * @param array $tools 工具列表
     * @param array $thoughts 思考历史
     * @param array $actions 动作历史
     * @param array $observations 观察历史
     * @return string
     */
    protected function buildReActThinkPrompt(string $task, array $tools, array $thoughts, array $actions, array $observations): string
    {
        $toolsDesc = implode(', ', $tools);
        
        $prompt = "You are a helpful assistant that can use tools to solve problems.\n\n";
        $prompt .= "Task: {$task}\n\n";
        $prompt .= "Available tools: {$toolsDesc}\n\n";
        
        if (!empty($thoughts)) {
            $prompt .= "Previous thoughts and actions:\n";
            for ($i = 0; $i < count($thoughts); $i++) {
                $prompt .= "Thought {$i}: {$thoughts[$i]}\n";
                if (isset($actions[$i])) {
                    $prompt .= "Action {$i}: {$actions[$i]['tool']} - {$actions[$i]['input']}\n";
                    $prompt .= "Observation {$i}: {$observations[$i]}\n";
                }
            }
        }
        
        $prompt .= "\nThink step by step. If you can provide a final answer, start with 'Final Answer:'. ";
        $prompt .= "If you need to use a tool, specify 'Action: [tool_name]' and 'Action Input: [input]'.";
        
        return $prompt;
    }
    
    /**
     * 从思考中解析动作
     * 
     * @param string $thought 思考内容
     * @return array
     */
    protected function parseActionFromThought(string $thought): array
    {
        if (strpos($thought, 'Final Answer:') !== false) {
            $answer = trim(substr($thought, strpos($thought, 'Final Answer:') + 13));
            return [
                'final_answer' => true,
                'answer' => $answer,
                'action' => null,
                'tool' => null,
                'action_input' => null
            ];
        }
        
        $action = null;
        $tool = null;
        $actionInput = null;
        
        if (preg_match('/Action:\s*([^\n]+)/', $thought, $matches)) {
            $tool = trim($matches[1]);
            $action = true;
        }
        
        if (preg_match('/Action Input:\s*([^\n]+)/', $thought, $matches)) {
            $actionInput = trim($matches[1]);
        }
        
        return [
            'final_answer' => false,
            'answer' => null,
            'action' => $action,
            'tool' => $tool,
            'action_input' => $actionInput
        ];
    }
    
    /**
     * 执行工具
     * 
     * @param string $toolName 工具名称
     * @param string $input 输入参数
     * @return string
     */
    protected function executeTool(string $toolName, string $input): string
    {
        if (!isset($this->tools[$toolName])) {
            return "Tool '{$toolName}' not found";
        }
        
        try {
            $tool = $this->tools[$toolName];
            return call_user_func($tool['function'], $input);
        } catch (\Exception $e) {
            return "Tool execution failed: {$e->getMessage()}";
        }
    }
    
    /**
     * 计算器工具
     * 
     * @param string $expression 数学表达式
     * @return string
     */
    protected function calculateTool(string $expression): string
    {
        try {
            // 简单的数学计算（安全起见，只支持基本运算）
            $expression = preg_replace('/[^0-9+\-*\/().\s]/', '', $expression);
            $result = eval("return {$expression};");
            return "计算结果: {$result}";
        } catch (\Exception $e) {
            return "计算错误: {$e->getMessage()}";
        }
    }
    
    /**
     * 网络搜索工具
     * 
     * @param string $query 搜索查询
     * @return string
     */
    protected function webSearchTool(string $query): string
    {
        // 这里应该集成真实的搜索API
        return "搜索结果: 关于'{$query}'的相关信息（模拟结果）";
    }
    
    /**
     * 文本分析工具
     * 
     * @param string $text 待分析文本
     * @return string
     */
    protected function textAnalyzerTool(string $text): string
    {
        try {
            $analysis = $this->basicAiService->analyzeText($text, 'summary');
            return "文本分析结果: {$analysis}";
        } catch (\Exception $e) {
            return "文本分析失败: {$e->getMessage()}";
        }
    }
    
    /**
     * 清除记忆
     * 
     * @param string|null $sessionId 会话ID，为null时清除所有记忆
     * @return void
     */
    public function clearMemory(?string $sessionId = null): void
    {
        if ($sessionId === null) {
            if (method_exists($this->memory, 'clearAll')) {
                $this->memory->clearAll();
            }
            Logger::info('All memory cleared');
        } else {
            $this->memory->clearSession($sessionId);
            Logger::info('Memory cleared for session', ['session_id' => $sessionId]);
        }
    }
    
    /**
     * 添加自定义工具
     * 
     * @param string $name 工具名称
     * @param string $description 工具描述
     * @param callable $function 工具函数
     * @return void
     */
    public function addTool(string $name, string $description, callable $function): void
    {
        $this->tools[$name] = [
            'name' => $name,
            'description' => $description,
            'function' => $function
        ];
        
        Logger::info('Custom tool added', ['tool_name' => $name]);
    }
    
    /**
     * 获取可用工具列表
     * 
     * @return array
     */
    public function getAvailableTools(): array
    {
        return array_keys($this->tools);
    }
    
    /**
     * 获取工具描述
     * 
     * @param string $toolName 工具名称
     * @return string|null
     */
    public function getToolDescription(string $toolName): ?string
    {
        return $this->tools[$toolName]['description'] ?? null;
    }
    
    /**
     * 设置记忆管理器
     * 
     * @param MemoryInterface $memory
     * @return void
     */
    public function setMemory(MemoryInterface $memory): void
    {
        $this->memory = $memory;
        Logger::info('Memory manager updated');
    }
    
    /**
     * 获取记忆管理器
     * 
     * @return MemoryInterface
     */
    public function getMemory(): MemoryInterface
    {
        return $this->memory;
    }
    
    /**
     * 使用模板创建LLM链
     * 
     * @param string $templateName 模板名称
     * @param array $variables 变量
     * @param array $options 选项参数
     * @param string|null $provider 指定服务提供商
     * @return array
     * @throws AiServiceException
     */
    public function createLLMChainWithTemplate(string $templateName, array $variables = [], array $options = [], ?string $provider = null): array
    {
        try {
            $template = PromptTemplateManager::get($templateName);
            return $this->createLLMChain($template, $variables, $options, $provider);
        } catch (\Exception $e) {
            Logger::error('LLM Chain with template execution failed', [
                'template_name' => $templateName,
                'error' => $e->getMessage()
            ]);
            throw new AiServiceException("LLM Chain with template execution failed: {$e->getMessage()}", 0, $e);
        }
    }
    
    /**
     * 带记忆和模板的对话
     * 
     * @param string $input 用户输入
     * @param string $sessionId 会话ID
     * @param string|PromptTemplateInterface|null $template 提示模板
     * @param array $options 选项参数
     * @param string|null $provider 指定服务提供商
     * @return string
     * @throws AiServiceException
     */
    public function chatWithMemoryAndTemplate(string $input, string $sessionId, $template = null, array $options = [], ?string $provider = null): string
    {
        try {
            // 获取记忆变量
            $memoryVariables = $this->memory->getMemoryVariables($sessionId);
            
            if ($template !== null) {
                // 使用模板
                $variables = array_merge($memoryVariables, ['input' => $input]);
                
                if (is_string($template)) {
                    $template = PromptTemplateManager::get($template);
                }
                
                $prompt = $template->format($variables);
                $response = $this->basicAiService->chat($prompt, $options, $provider);
            } else {
                // 直接使用记忆对话
                $response = $this->executeWithMemory($input, $sessionId, $options, $provider);
            }
            
            return $response;
        } catch (\Exception $e) {
            Logger::error('Chat with memory and template failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            throw new AiServiceException("Chat with memory and template failed: {$e->getMessage()}", 0, $e);
        }
    }
    
    /**
     * 获取记忆摘要
     * 
     * @param string $sessionId 会话ID
     * @return string
     */
    public function getMemorySummary(string $sessionId): string
    {
        return $this->memory->summarizeHistory($sessionId);
    }
    
    /**
     * 保存上下文到记忆
     * 
     * @param string $sessionId 会话ID
     * @param string $key 键名
     * @param mixed $value 值
     * @return void
     */
    public function saveContextToMemory(string $sessionId, string $key, $value): void
    {
        $this->memory->saveContext($sessionId, $key, $value);
    }
    
    /**
     * 从记忆获取上下文
     * 
     * @param string $sessionId 会话ID
     * @param string $key 键名
     * @return mixed
     */
    public function getContextFromMemory(string $sessionId, string $key)
    {
        return $this->memory->getContext($sessionId, $key);
    }
    
    /**
     * 创建MySQL存储器
     * @param string $environment 环境配置
     * @param array $customConfig 自定义配置
     * @return MemoryInterface
     */
    public function createMySqlMemory(string $environment = null, array $customConfig = []): MemoryInterface
    {
        return MySqlMemoryFactory::create($environment, $customConfig);
    }
    
    /**
     * 切换到MySQL存储器
     * @param string $environment 环境配置
     * @param array $customConfig 自定义配置
     * @return void
     */
    public function switchToMySqlMemory(string $environment = null, array $customConfig = []): void
    {
        $this->memory = $this->createMySqlMemory($environment, $customConfig);
        Logger::info('Switched to MySQL memory storage', [
            'environment' => $environment ?: 'auto',
            'storage_type' => 'MySQL'
        ]);
    }
    
    /**
     * 切换到内存存储器
     * @return void
     */
    public function switchToBufferMemory(): void
    {
        $this->memory = new ConversationBufferMemory();
        Logger::info('Switched to buffer memory storage', [
            'storage_type' => 'Buffer'
        ]);
    }
    
    /**
     * 获取当前存储器类型
     * @return string
     */
    public function getMemoryType(): string
    {
        if ($this->memory instanceof MySqlMemory) {
            return 'MySQL';
        } elseif ($this->memory instanceof ConversationBufferMemory) {
            return 'Buffer';
        } else {
            return get_class($this->memory);
        }
    }
    
    /**
     * 获取存储器统计信息
     * @return array
     */
    public function getMemoryStats(): array
    {
        $stats = [
            'type' => $this->getMemoryType(),
            'class' => get_class($this->memory)
        ];
        
        // 如果是MySQL存储器，获取详细统计
        if ($this->memory instanceof MySqlMemory) {
            $mysqlStats = $this->memory->getStorageStats();
            $stats = array_merge($stats, $mysqlStats);
        }
        
        return $stats;
    }
    
    /**
     * 清理过期会话（仅适用于MySQL存储器）
     * @param int $daysOld 多少天前的会话
     * @return int 清理的会话数
     */
    public function cleanupExpiredSessions(int $daysOld = 30): int
    {
        if (!($this->memory instanceof MySqlMemory)) {
            Logger::warning('Cleanup operation only available for MySQL memory storage');
            return 0;
        }
        
        return $this->memory->cleanupExpiredSessions($daysOld);
    }
    
    /**
     * 获取所有会话列表（仅适用于MySQL存储器）
     * @param int $limit 限制数量
     * @param int $offset 偏移量
     * @return array
     */
    public function getAllSessions(int $limit = 50, int $offset = 0): array
    {
        if (!($this->memory instanceof MySqlMemory)) {
            Logger::warning('Session listing only available for MySQL memory storage');
            return [];
        }
        
        return $this->memory->getAllSessions($limit, $offset);
    }
}