<?php

namespace app\ai\services;

use app\ai\utils\Logger;

/**
 * 中文分词服务
 * 支持多种分词方案，提高关键词提取准确性
 */
class ChineseSegmentationService
{
    /**
     * 停用词列表
     * @var array
     */
    protected array $stopWords = [
        // 基础停用词
        '的', '了', '在', '是', '我', '你', '他', '她', '它', '们', '这', '那', '有', '没', '不', '要', '会', '能', '可以',
        // 疑问词
        '怎么', '什么', '为什么', '如何', '哪里', '哪个', '什么时候', '多少', '几个',
        // 语气词
        '啊', '呢', '吧', '嘛', '呀', '哦', '额', '嗯', '哈',
        // 连接词
        '和', '与', '或', '但是', '然后', '因为', '所以', '如果', '虽然', '但是',
        // 时间词
        '今天', '明天', '昨天', '现在', '以前', '以后', '刚才', '马上', '立即',
        // 程度词
        '很', '非常', '特别', '比较', '相当', '十分', '极其', '最', '更',
        // 其他
        '一个', '一些', '一点', '一下', '一直', '一起', '一样', '还是', '就是', '只是', '而且', '或者'
    ];

    /**
     * 高频无意义词
     * @var array
     */
    protected array $meaninglessWords = [
        '请问', '您好', '你好', '谢谢', '不好意思', '麻烦', '帮忙', '一下', '看看', '知道', '告诉'
    ];

    /**
     * 提取关键词（主方法）
     * @param string $text
     * @param int $maxWords 最大关键词数量
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 尝试多种分词方法
        $keywords = [];
        
        // 方法1: 基于规则的分词
        $ruleBasedKeywords = $this->ruleBasedSegmentation($cleanText);
        $keywords = array_merge($keywords, $ruleBasedKeywords);
        
        // 方法2: N-gram 分词
        $ngramKeywords = $this->ngramSegmentation($cleanText);
        $keywords = array_merge($keywords, $ngramKeywords);
        
        // 方法3: 基于词典的分词
        $dictKeywords = $this->dictionaryBasedSegmentation($cleanText);
        $keywords = array_merge($keywords, $dictKeywords);
        
        // 3. 过滤和排序
        $filteredKeywords = $this->filterAndRankKeywords($keywords, $cleanText);
        
        // 4. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 转换为小写
        $text = mb_strtolower($text);
        
        // 移除特殊字符，保留中文、英文、数字
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $text);
        
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 基于规则的分词
     * @param string $text
     * @return array
     */
    protected function ruleBasedSegmentation(string $text): array
    {
        $keywords = [];
        
        // 1. 提取连续的中文词汇（2-4个字符）
        preg_match_all('/[\p{Han}]{2,4}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        // 2. 提取英文单词
        preg_match_all('/[a-zA-Z]{2,}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        // 3. 提取数字
        preg_match_all('/\d+/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        return $keywords;
    }

    /**
     * N-gram 分词
     * @param string $text
     * @return array
     */
    protected function ngramSegmentation(string $text): array
    {
        $keywords = [];
        $length = mb_strlen($text);
        
        // 2-gram
        for ($i = 0; $i < $length - 1; $i++) {
            $gram = mb_substr($text, $i, 2);
            if ($this->isValidKeyword($gram)) {
                $keywords[] = $gram;
            }
        }
        
        // 3-gram
        for ($i = 0; $i < $length - 2; $i++) {
            $gram = mb_substr($text, $i, 3);
            if ($this->isValidKeyword($gram)) {
                $keywords[] = $gram;
            }
        }
        
        return $keywords;
    }

    /**
     * 基于词典的分词
     * @param string $text
     * @return array
     */
    protected function dictionaryBasedSegmentation(string $text): array
    {
        // 常见的业务相关词汇
        $businessDict = [
            '购买', '支付', '充值', '退款', '订单', '商品', '价格', '优惠', '折扣', '会员',
            '登录', '注册', '密码', '账户', '个人', '设置', '修改', '绑定', '验证', '安全',
            '测试', '功能', '使用', '操作', '步骤', '方法', '教程', '说明', '帮助', '问题',
            '客服', '联系', '咨询', '反馈', '建议', '投诉', '处理', '解决', '回复', '服务',
            '系统', '平台', '网站', '应用', '软件', '程序', '版本', '更新', '升级', '维护',
            '淘宝', '天猫', '京东', '拼多多', '微信', '支付宝', '银行', '信用卡', '花呗', '借呗'
        ];
        
        $keywords = [];
        
        foreach ($businessDict as $word) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 过滤和排序关键词
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function filterAndRankKeywords(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无意义词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateKeywordWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 判断是否为有效关键词
     * @param string $keyword
     * @return bool
     */
    protected function isValidKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 8) {
            return false;
        }
        
        // 停用词检查
        if (in_array($keyword, $this->stopWords)) {
            return false;
        }
        
        // 无意义词检查
        if (in_array($keyword, $this->meaninglessWords)) {
            return false;
        }
        
        // 纯空格检查
        if (trim($keyword) === '') {
            return false;
        }
        
        // 纯数字且长度小于3的过滤
        if (is_numeric($keyword) && $length < 3) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateKeywordWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 频率权重
        $frequency = substr_count($text, $keyword);
        $weight += $frequency * 0.3;
        
        // 2. 长度权重（更长的词汇权重更高）
        $length = mb_strlen($keyword);
        $weight += ($length - 1) * 0.2;
        
        // 3. 位置权重（出现在前面的权重更高）
        $position = strpos($text, $keyword);
        if ($position !== false) {
            $textLength = mb_strlen($text);
            $positionWeight = 1 - ($position / $textLength);
            $weight += $positionWeight * 0.3;
        }
        
        // 4. 业务相关性权重
        $businessKeywords = ['购买', '支付', '充值', '退款', '测试', '功能', '登录', '注册', '客服', '帮助'];
        if (in_array($keyword, $businessKeywords)) {
            $weight += 0.5;
        }
        
        return $weight;
    }

    /**
     * 获取文本摘要关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function getSummaryKeywords(string $text, int $maxWords = 5): array
    {
        $keywords = $this->extractKeywords($text, $maxWords * 2);
        
        // 进一步筛选最重要的关键词
        $important = [];
        foreach ($keywords as $keyword) {
            $weight = $this->calculateKeywordWeight($keyword, $text);
            if ($weight > 0.5) { // 只保留高权重的关键词
                $important[] = $keyword;
            }
        }
        
        return array_slice($important, 0, $maxWords);
    }

    /**
     * 调试信息：显示分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'rule_based' => $this->ruleBasedSegmentation($cleanText),
            'ngram' => $this->ngramSegmentation($cleanText),
            'dictionary' => $this->dictionaryBasedSegmentation($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
